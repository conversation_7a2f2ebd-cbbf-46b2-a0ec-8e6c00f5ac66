import os
import re
import sys
import json
import atexit
import datetime
import requests
from instagrapi import Client
from signal import signal, SIGTERM
from multiprocessing import Manager
from multiprocessing.util import _exit_function
from concurrent.futures import ThreadPoolExecutor
from dankware import err, clr, get_duration
from flask import Flask, Response, request, render_template, render_template_string

'''
400 Bad Request
401 Unauthorized / Unauthenticated
403 Forbidden / Banned
404 Not Found
500 Internal Server Error
'''

os.chdir(os.path.dirname(__file__))
start_time = datetime.datetime.now()

# SIGTERM

# def restart():
#     time.sleep(2592000)
#     while True:
#         try: requests.get("https://api.render.com/deploy/srv-cdbf7c1a6gdlcsrvk67g?key=4HFiy817kLE", timeout=10); break
#         except: time.sleep(10)

def shutdown(*args):
    print(clr("  - SIGTERM Received!",2))
    try: requests.post(server_webhook, json={"content": f"```❌ dank.local-server #{os.getppid()}-{os.getpid()} shutting down...```"}, timeout=3) # removed executor.submit
    except Exception as exc: print(clr(f'  - [ERROR] server_webhook: {str(exc)}',2))
    global running
    running = False
    executor.shutdown(wait=False, cancel_futures=True)
    print(clr("  - Exiting...",2))
    sys.exit(0)

atexit.unregister(_exit_function)
atexit.register(shutdown)
signal(SIGTERM, shutdown)

# START
# variables
running = True
manager = Manager()
credentials = json.loads(open('./assets/credentials_2.json','r',encoding='utf-8').read())
# webhooks
server_webhook = credentials['server_webhook']
server_error_webhook = credentials['server_error_webhook']
# instagram
instagram_username = credentials['instagram_username']
instagram_password = credentials['instagram_password']

# app config
print(clr("  - Loading Flask..."))
app = Flask(__name__)
executor = ThreadPoolExecutor(8)

# routes

@app.route('/', methods=['GET'])
def index():
    return render_template('index.html'), 200

# instagram

client = Client()
instagram_success = False
instagram_clips = manager.list()
try:
    if client.login(instagram_username, instagram_password):
        print(clr("  - Instagram login successful!"))
        instagram_success = True
    else:
        print(clr("  - Instagram login failed!",2))
except Exception as exc:
    client = None
    error = err((type(exc), exc, exc.__traceback__),'mini')
    requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Instagram Login Failed ⚠️ --->\n\n{error}```"}, timeout=3)
del instagram_username, instagram_password

def instagram_uploader(last_message):
    try:
        if last_message.clip.image_versions2:
            thumbnail_path = client.photo_download_by_url(last_message.clip.image_versions2['candidates'][0]['url'])
        else:
            thumbnail_path = None
        clip_path = client.clip_download(last_message.clip.pk)
        _ = client.clip_upload(clip_path, re.sub(r'@[a-z0-9._]+', '', last_message.clip.caption_text), thumbnail_path)
        instagram_clips.append(last_message.clip.pk)
        os.remove(thumbnail_path)
        os.remove(clip_path)
    except Exception as exc:
        try: client.logout()
        except: pass
        global instagram_success
        instagram_success = False
        print(clr(f'  - Instagram upload failed: {str(exc)}',2))
        error = err((type(exc), exc, exc.__traceback__),'mini')
        requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Instagram Upload Failed ⚠️ --->\n\n{error}```"}, timeout=3)

@app.route('/instagram-upload', methods=['GET'])
def instagram_upload():
    if instagram_success:
        try:
            last_message = client.direct_messages(340282366841710301244259516440940995898, 1)
            if len(last_message):
                last_message = last_message[0]
                if last_message.item_type == 'clip' and last_message.clip.pk not in instagram_clips:
                    executor.submit(instagram_uploader, last_message)
                    return Response('Instagram upload successful!', mimetype='text/plain'), 200
            return Response('Instagram upload skipped!', mimetype='text/plain'), 200
        except Exception as exc:
            # try: client.logout()
            # except: pass
            # instagram_success = False
            print(clr(f'  - Instagram upload failed: {str(exc)}',2))
            error = err((type(exc), exc, exc.__traceback__),'mini')
            requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Instagram Upload Failed ⚠️ --->\n\n{error}```"}, timeout=3)
            return Response('Instagram upload failed!', mimetype='text/plain'), 500
    return Response('Instagram login failed!', mimetype='text/plain'), 500

@app.route('/shutdown', methods=['GET'])
def instagram_logout():
    global instagram_success
    if instagram_success:
        try: client.logout()
        except: pass
        instagram_success = False
        return Response('Logged out!', mimetype='text/plain'), 200
    return Response('Already logged out!', mimetype='text/plain'), 200

end_time = datetime.datetime.now()
try: requests.post(server_webhook, json={"content": f"```✅ dank.local-server online! #{os.getppid()}-{os.getpid()} took {get_duration(start_time, end_time, interval='seconds')}s!```"}, timeout=3) # removed executor.submit
except Exception as exc: print(clr(f'  - [ERROR] server_webhook: {str(exc)}',2))

if __name__ == "__main__":
    print(clr("  - Starting Flask App..."))
    app.run(host='0.0.0.0', port=int(os.environ.get("PORT", 10000)), threaded=True) # ssl_context='adhoc'
