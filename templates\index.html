<!DOCTYPE html>
<html lang="en-US" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Welcome to SirDank's official website! Discover Minecraft Java and Bedrock server lists. Visit my GitHub for additional projects and resources.">
    <meta name="keywords" content="SirDank, Dankware, Minecraft Server Scanner, Minecraft Server Builder">
    <meta name="author" content="SirDank">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="google-site-verification" content="vJvied_w74OhiDdOZcHZ42mCWbcu_l6mbnLNR4EffAM" />
    <title>Dankware</title>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Source Code Pro:wght@400;700&family=Pirata+One&display=swap" rel="stylesheet">
    <!-- Common CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common.css') }}">
    <style>
        /* Homepage specific styles */
        body {
            overflow: hidden;
        }

        .bg_video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -2;
        }

        .sirdank_gif {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            object-fit: contain;
            opacity: 0.7;
            z-index: 1;
            max-width: 500px;
            max-height: 500px;
            transition: all 0.3s ease;
            /* Removed the filter to fix the glow issue */
        }

        .buttons-container {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            gap: 15px;
            z-index: 10;
        }

        .button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.7);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            outline: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px var(--glow-color);
        }

        .button:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px var(--glow-color);
        }

        @media (max-width: 768px) {
            .button {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .sirdank_gif {
                max-width: 300px;
                max-height: 300px;
                transform: translate(-50%, -50%);
            }
        }
    </style>
</head>
<body>
    <!-- Page Preloader -->
    <div class="preloader" aria-hidden="true">
        <div class="loader" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Navigation bar removed as requested -->

    <!-- Background Video -->
    <video id="bgVideo" class="bg_video" autoplay loop muted playsinline>
        <source src="https://github.com/SirDank/dank.site/raw/main/Resources/aesthetic_1080.mp4" type="video/mp4">
        <source src="https://github.com/SirDank/dank.site/raw/main/Resources/aesthetic_1080.webm" type="video/webm">
        <p class="warning">Your browser does not support HTML5 video.</p>
    </video>

    <!-- Background Image -->
    <img class="sirdank_gif" src="https://github.com/SirDank/dank.site/raw/main/Resources/sir.dank.gif" alt="SirDank" title="SirDank">

    <!-- Main Content area intentionally left empty -->

    <!-- Social Media and Navigation Buttons -->
    <div class="buttons-container">
        <a class="button" href="https://github.com/SirDank" target="_blank" rel="noopener" title="GitHub Profile" aria-label="GitHub Profile">
            <img src="{{ url_for('static', filename='images/github.png') }}" alt="Github Profile" width="30" height="30">
        </a>
        <a class="button" href="/discord" title="Discord Server" aria-label="Discord Server">
            <img src="{{ url_for('static', filename='images/discord.png') }}" alt="Discord Server" width="30" height="30">
        </a>
        <a class="button" href="/youtube" title="YouTube Channel" aria-label="YouTube Channel">
            <img src="{{ url_for('static', filename='images/youtube.png') }}" alt="YouTube Channel" width="30" height="30">
        </a>
        <a class="button" href="/minecraft-java-servers" title="Java Servers" aria-label="Minecraft Java Servers">
            <img src="{{ url_for('static', filename='images/grass.png') }}" alt="Java Servers" width="30" height="30">
        </a>
        <a class="button" href="/minecraft-bedrock-servers" title="Bedrock Servers" aria-label="Minecraft Bedrock Servers">
            <img src="{{ url_for('static', filename='images/bedrock.png') }}" alt="Bedrock Servers" width="30" height="30">
        </a>
        <a class="button" href="/system-stats" title="System Stats" aria-label="System Statistics">
            <i class="fas fa-chart-line" aria-hidden="true"></i>
        </a>
        <button class="button audio-button" id="audioButton" onclick="toggleMute()" title="Toggle Audio" aria-label="Toggle Background Audio">
            <i class="fas fa-volume-mute" aria-hidden="true"></i>
        </button>
    </div>

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Custom Scripts -->
    <script>
        // Audio toggle functionality for background video
        (function() {
            const bgVideo = document.getElementById('bgVideo');
            const audioButton = document.getElementById('audioButton');
            const audioIcon = audioButton.querySelector('i');

            // Make toggleMute available globally for the onclick handler
            window.toggleMute = function() {
                bgVideo.muted = !bgVideo.muted;
                audioIcon.className = bgVideo.muted ? 'fas fa-volume-mute' : 'fas fa-volume-up';
            };
        })();

        // Handle preloader fade-out
        window.addEventListener('load', function() {
            const preloader = document.querySelector('.preloader');
            preloader.style.opacity = '0';
            setTimeout(function() {
                preloader.style.display = 'none';
            }, 500);
        });
    </script>
</body>
</html>
