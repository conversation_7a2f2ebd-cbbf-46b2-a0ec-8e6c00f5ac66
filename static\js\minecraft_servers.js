$(document).ready(function() {
    // Get table information
    const tableId = $('table').attr('id');
    const table = $(`#${tableId}`);

    // Get current URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const currentSortBy = urlParams.get('sort_by') || 'last_checked';
    const currentSortOrder = urlParams.get('sort_order') || 'desc';

    // Handle per page selector change
    $('#perPageSelect').on('change', function() {
        const perPage = $(this).val();
        // Update URL with new per_page value and reset to page 1
        urlParams.set('per_page', perPage);
        urlParams.set('page', 1); // Reset to first page when changing items per page

        // Preserve search query if it exists
        const searchQuery = urlParams.get('search');
        if (searchQuery) {
            urlParams.set('search', searchQuery);
        }

        window.location.href = `${window.location.pathname}?${urlParams.toString()}`;
    });

    // Handle sort by selector change
    $('#sortBySelect').on('change', function() {
        const sortBy = $(this).val();
        urlParams.set('sort_by', sortBy);
        urlParams.set('page', 1); // Reset to first page when changing sort

        // Preserve search query if it exists
        const searchQuery = urlParams.get('search');
        if (searchQuery) {
            urlParams.set('search', searchQuery);
        }

        window.location.href = `${window.location.pathname}?${urlParams.toString()}`;
    });

    // Handle sort order selector change
    $('#sortOrderSelect').on('change', function() {
        const sortOrder = $(this).val();
        urlParams.set('sort_order', sortOrder);
        urlParams.set('page', 1); // Reset to first page when changing sort order

        // Preserve search query if it exists
        const searchQuery = urlParams.get('search');
        if (searchQuery) {
            urlParams.set('search', searchQuery);
        }

        window.location.href = `${window.location.pathname}?${urlParams.toString()}`;
    });

    // Initialize visual sorting indicators
    function initializeSortingIndicators() {
        // Add sort indicators to column headers for visual feedback only
        $(`#${tableId} thead th`).each(function(index) {
            const columnName = getColumnNameByIndex(index);
            if (columnName) {
                $(this).append(`<span class="sort-icon ms-1"></span>`);

                // If this is the current sort column, add the appropriate icon
                if (columnName === currentSortBy) {
                    const icon = currentSortOrder === 'asc' ? '↑' : '↓';
                    $(this).find('.sort-icon').text(icon);
                    $(this).addClass('sorting-active');
                }
            }
        });
    }

    // Initialize DataTable with minimal configuration for styling only
    const dataTable = table.DataTable({
        responsive: false, // Disable responsive behavior to show all columns
        scrollX: true,
        paging: false, // Disable DataTables paging since we're using server-side pagination
        searching: false, // Disable client-side searching since we're using server-side search
        ordering: false, // Disable DataTables sorting since we're using server-side sorting
        info: false, // Disable DataTables info since we're showing our own
        dom: 'rt<"clear">', // Only show table (removed filter and other controls)
        scrollCollapse: true,
        autoWidth: true,
        /**
         * Called once when the table is fully initialized
         * Handles styling and preloader hiding
         */
        initComplete: function() {
            // Initialize visual sorting indicators
            initializeSortingIndicators();

            // Hide the preloader with a fade-out effect
            setTimeout(function() {
                $('.preloader').css('opacity', '0');
                setTimeout(function() {
                    $('.preloader').css('display', 'none');
                }, 500);
            }, 300);
        }
    });

    // Helper function to map column index to column name for sorting
    function getColumnNameByIndex(index) {
        if (tableId === 'javaTable') {
            const javaColumns = ['ip', 'version', 'players', 'latency', 'description', 'city', 'org', 'domain', 'last_checked'];
            return javaColumns[index];
        } else {
            const bedrockColumns = ['ip', 'version', 'players', 'latency', 'gamemode', 'map', 'motd', 'city', 'org', 'domain', 'last_checked'];
            return bedrockColumns[index];
        }
    }

    // Handle window resize
    $(window).resize(debounce(function() {
        dataTable.columns.adjust().draw();
    }, 250));

    // Ensure columns are properly sized on initial load
    setTimeout(function() {
        dataTable.columns.adjust().draw();
    }, 100);



    // Debounce function to limit how often resize handler fires
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }
});
