import os
import json
import time
import datetime
import psutil
import requests
from sqlalchemy.pool import Singleton<PERSON>hreadPool
from werkzeug.middleware.proxy_fix import ProxyFix
from dankware import clr, err
from sqlmodel import SQLModel, Field, Session, create_engine, select
from flask import Flask, Response, render_template, request, stream_with_context
from sqlalchemy import or_, func, text, Integer, DateTime

'''
400 Bad Request
401 Unauthorized / Unauthenticated
403 Forbidden / Banned
404 Not Found
500 Internal Server Error
'''

# minecraft servers

class Java(SQLModel, table=True):
    ip: str = Field(primary_key=True)
    version: str
    players: str
    latency: int
    description: str
    city: str
    org: str
    domain: str
    last_checked: datetime.datetime = Field(sa_type=DateTime)

class Bedrock(SQLModel, table=True):
    ip: str = Field(primary_key=True)
    version: str
    players: str
    latency: int
    gamemode: str
    map: str
    motd: str
    city: str
    org: str
    domain: str
    last_checked: datetime.datetime = Field(sa_type=DateTime)

# START
# variables
running = True

# app config
print(clr("  - Loading Flask..."))
app = Flask(__name__, static_folder='static')
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1)

# minecraft servers
engine = create_engine("sqlite:///mc_servers_debug.db", connect_args={"check_same_thread": False, "timeout": 60}, poolclass=SingletonThreadPool, pool_size=100)

# routes

@app.route('/', methods=['GET'])
def index():
    return render_template('index.html'), 200

@app.route('/system-stats', methods=['GET'])
def system_stats():
    return render_template('system_stats.html'), 200

@app.route('/system-stats-data')
def system_stats_data():
    def generate_stats():
        # Store previous network counters for calculating rates
        prev_net_io = psutil.net_io_counters()
        prev_time = time.time()

        while True:
            try:
                # Get current time
                current_time = time.time()
                time_diff = current_time - prev_time
                current_time_str = datetime.datetime.now().strftime('%H:%M:%S')

                # CPU stats
                cpu_percent = psutil.cpu_percent(interval=None)
                cpu_freq = psutil.cpu_freq()
                cpu_count = psutil.cpu_count(logical=True)
                cpu_count_physical = psutil.cpu_count(logical=False)

                # Memory stats
                memory = psutil.virtual_memory()

                # Disk stats
                disk = psutil.disk_usage('/')

                # Network stats
                net_io = psutil.net_io_counters()
                sent_bytes = net_io.bytes_sent - prev_net_io.bytes_sent
                recv_bytes = net_io.bytes_recv - prev_net_io.bytes_recv

                # Prevent division by zero
                if time_diff > 0.001:  # Use a small threshold instead of exactly zero
                    sent_kb = sent_bytes / 1024 / time_diff
                    recv_kb = recv_bytes / 1024 / time_diff
                else:
                    sent_kb = 0
                    recv_kb = 0

                # System uptime
                boot_time = datetime.datetime.fromtimestamp(psutil.boot_time())
                uptime = datetime.datetime.now() - boot_time
                uptime_str = f"{uptime.days}d {uptime.seconds // 3600}h {(uptime.seconds // 60) % 60}m"

                # Process information (top 10 by CPU usage)
                # First, update CPU usage for all processes
                for proc in psutil.process_iter(['pid']):
                    try:
                        proc.cpu_percent(interval=0)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                # Small delay to allow CPU percentages to be calculated
                time.sleep(0.1)

                processes = []
                for proc in sorted(psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'create_time', 'status']),
                                key=lambda p: p.info['cpu_percent'] or 0, reverse=True)[:10]:
                    try:
                        proc_info = proc.info
                        if proc_info['create_time']:
                            created_time = datetime.datetime.fromtimestamp(proc_info['create_time']).strftime('%Y-%m-%d %H:%M')
                        else:
                            created_time = 'N/A'

                        # Get CPU percent - divide by CPU count to get per-core percentage
                        cpu_pct = proc_info['cpu_percent'] or 0
                        if cpu_count > 0:  # Avoid division by zero
                            cpu_pct = cpu_pct / cpu_count

                        processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'username': proc_info['username'] or 'N/A',
                            'cpu_percent': cpu_pct,
                            'memory_percent': proc_info['memory_percent'] or 0,
                            'created': created_time,
                            'status': proc_info['status'] or 'N/A'
                        })
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass

                # Convert bytes to GB and MB for better readability
                def bytes_to_gb(bytes_value):
                    return round(bytes_value / (1024 ** 3), 2)

                def bytes_to_mb(bytes_value):
                    return int(bytes_value / (1024 ** 2))

                # Get CPU frequency (handle None values)
                current_freq = 0
                max_freq = 0
                if cpu_freq:
                    # CPU frequency is typically reported in MHz, convert to GHz
                    current_freq = (cpu_freq.current / 1000) if cpu_freq.current else 0
                    max_freq = (cpu_freq.max / 1000) if cpu_freq.max else 0

                # Prepare data to send
                data = {
                    'time': current_time_str,
                    'cpu': {
                        'usage': cpu_percent,
                        'freq_current': round(current_freq, 2),
                        'freq_max': round(max_freq, 2),
                        'cores_logical': cpu_count,
                        'cores_physical': cpu_count_physical
                    },
                    'memory': {
                        'total': memory.total,
                        'available': memory.available,
                        'used': memory.used,
                        'percent': memory.percent,
                        'total_gb': bytes_to_gb(memory.total),
                        'used_gb': bytes_to_gb(memory.used),
                        'available_gb': bytes_to_gb(memory.available),
                        'total_mb': bytes_to_mb(memory.total),
                        'used_mb': bytes_to_mb(memory.used),
                        'available_mb': bytes_to_mb(memory.available)
                    },
                    'disk': {
                        'total': disk.total,
                        'used': disk.used,
                        'free': disk.free,
                        'percent': disk.percent,
                        'total_gb': bytes_to_gb(disk.total),
                        'used_gb': bytes_to_gb(disk.used),
                        'free_gb': bytes_to_gb(disk.free)
                    },
                    'network': {
                        'sent_kb': round(sent_kb, 2),
                        'recv_kb': round(recv_kb, 2)
                    },
                    'system': {
                        'uptime': uptime_str
                    },
                    'processes': processes
                }

                # Update previous values for next iteration
                prev_net_io = net_io
                prev_time = current_time

                # Send data
                yield f"data:{json.dumps(data)}\n\n"
            except Exception as e:
                print(clr(f"Error generating stats: {str(e)}", 2))
                # Send a minimal error response to keep the connection alive
                yield f"data:{{\"error\": \"{str(e)}\"}}\n\n"

            # Sleep to prevent CPU overuse
            time.sleep(1)

    response = Response(stream_with_context(generate_stats()), mimetype="text/event-stream")
    response.headers["Cache-Control"] = "no-cache"
    response.headers["X-Accel-Buffering"] = "no"
    return response

@app.route('/dank-tool-suggestions', methods=['GET','POST'])
def dank_tool_suggestions_poster():

    if request.method == "GET":
        return render_template('dank_tool_suggestions.html'), 200

    try:
        # requests.post(chatroom_webhook, json={"content": f"```<--- 💡 --->\n\n{request.form['text']}```"}, timeout=3)
        return render_template('dank_tool_suggestions_success.html'), 200
    except Exception as exc:
        print(clr(f'  - [ERROR] chatroom_webhook: {str(exc)}',2))
        return render_template('dank_tool_suggestions_error.html'), 500

# Apply sorting based on parameters
# Define a function to get the order by clause
def get_order_by(field, sort_order):
    return field.desc() if sort_order == "desc" else field

def get_paginated_servers(server_type, page=1, per_page=10, sort_by="last_checked", sort_order="desc", search_query=None):
    """
    Get paginated server data for either Java or Bedrock servers

    Args:
        server_type (str): Either "java" or "bedrock"
        page (int): Current page number (1-based)
        per_page (int): Number of items per page
        sort_by (str): Column to sort by
        sort_order (str): Sort direction ("asc" or "desc")
        search_query (str): Optional search query to filter results

    Returns:
        dict: Dictionary containing paginated server data and pagination metadata
    """
    try:
        # Determine model based on server type
        model = Java if server_type == "java" else Bedrock

        # Calculate offset for pagination
        offset = (page - 1) * per_page

        with Session(engine) as session:
            # Build base query
            query = select(model)

            # Apply search filter if provided
            if search_query and search_query.strip():
                search_term = f"%{search_query.strip().lower()}%"

                # Create case-insensitive search conditions using ilike
                search_conditions = [
                    model.ip.ilike(search_term),
                    model.version.ilike(search_term),
                    model.players.ilike(search_term),
                    model.city.ilike(search_term),
                    model.org.ilike(search_term),
                    model.domain.ilike(search_term)
                ]

                # Add model-specific search fields
                if server_type == "java":
                    search_conditions.append(model.description.ilike(search_term))
                else:  # bedrock
                    search_conditions.extend([
                        model.gamemode.ilike(search_term),
                        model.map.ilike(search_term),
                        model.motd.ilike(search_term)
                    ])

                # Apply OR-combined search condition
                query = query.where(or_(*search_conditions))

            # Get total count for pagination
            count_query = select(func.count(model.ip)) # pylint: disable=not-callable
            if query._where_criteria:
                count_query = count_query.where(*query._where_criteria)
            total_count = session.exec(count_query).one()

            match sort_by:
                case "ip":
                    query = query.order_by(get_order_by(model.ip, sort_order))
                case "version":
                    query = query.order_by(get_order_by(model.version, sort_order))
                case "players":
                    query = query.order_by(
                        get_order_by(func.cast( # pylint: disable=not-callable
                            func.substr(model.players, 1, func.instr(model.players, '/') - 1), type_=Integer
                        ), sort_order)
                    )
                case "latency":
                    query = query.order_by(get_order_by(model.latency, sort_order))
                case "city":
                    query = query.order_by(get_order_by(model.city, sort_order))
                case "org":
                    query = query.order_by(get_order_by(model.org, sort_order))
                case "domain":
                    query = query.order_by(get_order_by(model.domain, sort_order))
                case "description" if server_type == "java":
                    query = query.order_by(get_order_by(model.description, sort_order))
                case _ if server_type == "bedrock":
                    match sort_by:
                        case "gamemode":
                            query = query.order_by(get_order_by(model.gamemode, sort_order))
                        case "map":
                            query = query.order_by(get_order_by(model.map, sort_order))
                        case "motd":
                            query = query.order_by(get_order_by(model.motd, sort_order))
                        case _:
                            query = query.order_by(get_order_by(model.last_checked, sort_order))
                case _:
                    query = query.order_by(get_order_by(model.last_checked, sort_order))

            # Apply pagination
            query = query.offset(offset).limit(per_page)

            # Execute query
            servers = session.exec(query).all()

            # Calculate pagination metadata
            total_pages = (total_count + per_page - 1) // per_page
            has_next = page < total_pages
            has_prev = page > 1

            # Format server data
            if server_type == "java":
                server_data = {
                    server.ip: {
                        'version': server.version,
                        'players': server.players,
                        'latency': server.latency,
                        'description': server.description,
                        'city': server.city,
                        'org': server.org,
                        'domain': server.domain,
                        'last_checked': server.last_checked.strftime("%d-%m-%Y %H:%M")
                    } for server in servers
                }
            else:  # bedrock
                server_data = {
                    server.ip: {
                        'version': server.version,
                        'players': server.players,
                        'latency': server.latency,
                        'gamemode': server.gamemode,
                        'map': server.map,
                        'motd': server.motd,
                        'city': server.city,
                        'org': server.org,
                        'domain': server.domain,
                        'last_checked': server.last_checked.strftime("%d-%m-%Y %H:%M")
                    } for server in servers
                }

            # Return data with pagination metadata
            return {
                "servers": server_data,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev,
                    "sort_by": sort_by,
                    "sort_order": sort_order
                }
            }
    except Exception as exc:
        error = err((type(exc), exc, exc.__traceback__), 'mini')
        print(clr(f"  - [ERROR] get_paginated_servers: {str(error)}", 2))
        return {"servers": {}, "pagination": {"page": 1, "total_pages": 1, "has_next": False, "has_prev": False}}

@app.route('/minecraft-java-servers', methods=['GET'])
def minecraft_java_servers():
    try:
        # Get pagination parameters from request
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        sort_by = request.args.get('sort_by', 'last_checked')
        sort_order = request.args.get('sort_order', 'desc')
        search_query = request.args.get('search', '')

        # Get paginated data
        data = get_paginated_servers("java", page, per_page, sort_by, sort_order, search_query)

        return render_template('minecraft_java_servers.html',
                              java_servers=data["servers"],
                              pagination=data["pagination"],
                              search_query=search_query)
    except Exception as exc:
        # error = err((type(exc), exc, exc.__traceback__),'mini')
        # try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Minecraft Java Servers --->\n\n{error}```"}, timeout=3)
        print(clr(f'  - [ERROR] minecraft_java_servers: {str(exc)}',2))

@app.route('/minecraft-bedrock-servers', methods=['GET'])
def minecraft_bedrock_servers():
    try:
        # Get pagination parameters from request
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        sort_by = request.args.get('sort_by', 'last_checked')
        sort_order = request.args.get('sort_order', 'desc')
        search_query = request.args.get('search', '')

        # Get paginated data
        data = get_paginated_servers("bedrock", page, per_page, sort_by, sort_order, search_query)

        return render_template('minecraft_bedrock_servers.html',
                              bedrock_servers=data["servers"],
                              pagination=data["pagination"],
                              search_query=search_query)
    except Exception as exc:
        # error = err((type(exc), exc, exc.__traceback__),'mini')
        # try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Minecraft Bedrock Servers --->\n\n{error}```"}, timeout=3)
        print(clr(f'  - [ERROR] minecraft_bedrock_servers: {str(exc)}',2))

if __name__ == "__main__":
    print(clr("  - Starting Flask App..."))
    app.run(host='0.0.0.0', port=int(os.environ.get("PORT", 10000))) # ssl_context='adhoc'
