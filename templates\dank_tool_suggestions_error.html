<!DOCTYPE html>
<html lang="en-US" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Error submitting suggestion">
    <meta name="author" content="SirDank">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Submission Error - Dankware</title>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Source Code Pro:wght@400;700&family=Pirata+One&display=swap" rel="stylesheet">
    <!-- Common CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common.css') }}">
    <style>
        /* Error page specific styles */

        .page-content {
            display: flex;
            min-height: 80vh;
            align-items: center;
            justify-content: center;
        }

        .error-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 2rem;
            border-radius: 10px;
            border: 1px solid var(--primary-color);
            box-shadow: 0 0 15px var(--glow-color);
            text-align: center;
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 1.5rem;
                margin: 0 15px;
            }
        }

        .back-button {
            display: inline-block;
            background-color: var(--primary-color);
            color: var(--text-color);
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            margin-top: 1.5rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background-color: #cc0000;
            box-shadow: 0 0 10px var(--glow-color);
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <!-- Page Preloader -->
    <div class="preloader" aria-hidden="true">
        <div class="loader" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="page-content">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-md-10 col-lg-8">

                <div class="error-container">
                    <h2>Submission Error</h2>
                    <p>Sorry, there was a problem submitting your suggestion. Please try again later.</p>
                    <a href="{{ url_for('dank_tool_suggestions_poster') }}" class="back-button">Try Again</a>
                </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Remove preloader when page is loaded
        window.addEventListener('load', function() {
            document.querySelector('.preloader').style.display = 'none';
        });
    </script>
</body>
</html>
