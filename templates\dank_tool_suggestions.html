<!DOCTYPE html>
<html lang="en-US" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Submit your suggestions for dank.tool">
    <meta name="author" content="SirDank">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Tool Suggestions - Dankware</title>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Source Code Pro:wght@400;700&family=Pirata+One&display=swap" rel="stylesheet">
    <!-- Common CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common.css') }}">
    <style>
        /* Tool Suggestions specific styles */

        .page-content {
            display: flex;
            min-height: 80vh;
            align-items: center;
            justify-content: center;
        }

        .suggestion-form {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 2rem;
            border-radius: 10px;
            border: 1px solid var(--primary-color);
            box-shadow: 0 0 15px var(--glow-color);
        }

        @media (max-width: 768px) {
            .suggestion-form {
                padding: 1.5rem;
                margin: 0 15px;
            }
        }

        .suggestion-form input[type="text"] {
            background-color: var(--bg-color);
            border: 1px solid var(--primary-color);
            color: var(--text-color);
            padding: 0.75rem;
            width: 100%;
            margin-bottom: 1rem;
            border-radius: 5px;
        }

        .suggestion-form input[type="submit"] {
            background-color: var(--primary-color);
            color: var(--text-color);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .suggestion-form input[type="submit"]:hover {
            background-color: #cc0000;
            box-shadow: 0 0 10px var(--glow-color);
        }
    </style>
</head>
<body>
    <!-- Page Preloader -->
    <div class="preloader" aria-hidden="true">
        <div class="loader" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="page-content">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-md-10 col-lg-8">

                <div class="suggestion-form">
                    <h2 class="text-center mb-4">Submit Your Suggestion</h2>
                    <form method="POST">
                        <div class="mb-3">
                            <input name="text" type="text" placeholder="Enter your suggestion here..." required>
                        </div>
                        <div class="text-center">
                            <input type="submit" value="Submit">
                        </div>
                    </form>
                </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Remove preloader when page is loaded
        window.addEventListener('load', function() {
            document.querySelector('.preloader').style.display = 'none';
        });
    </script>
</body>
</html>
