import socketio
from dankware import clr, cls

sio = socketio.Client()

@sio.event
def connect():
    print('Connected to server')

@sio.event
def disconnect():
    print('Disconnected from server')

@sio.event
def message(data):
    print(clr(data))

def main():
    cls()
    username = input("Enter your username: ")

    @sio.event
    def connect():
        sio.emit('message', f"{username} has joined the chat!")
        sio.emit('message', f"{username} has joined the chat!")

    sio.connect('http://127.0.0.1:10000')

    while True:
        message = input()
        sio.send(f"{username}: {message}")

if __name__ == "__main__":
    main()
