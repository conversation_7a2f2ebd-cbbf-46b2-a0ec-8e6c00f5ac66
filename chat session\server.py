import os
import json
from dankware import clr
from flask import Flask, render_template, request
from flask_socketio import <PERSON><PERSON><PERSON>, send, emit

app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
socketio = SocketIO(app)
os.chdir(os.path.dirname(__file__))

@socketio.on('connect')
def handle_connect():
    emit('message', f"{request.sid} has joined the chat!", broadcast=True)

@socketio.on('disconnect')
def handle_disconnect():
    emit('message', f"s{request.sid} has left the chat!", broadcast=True)

@socketio.on('message')
def handle_message(message):
    send(message, broadcast=True)
    print((f"{request.sid} | {request.remote_addr} | {request.remote_user}"))

@app.route('/') 
def home():
    try: print((clr(f'{request.remote_addr} | {request.remote_user}')))
    except: pass
    try: print((clr(f'{request.headers['X-Forwarded-For']}')))
    except: pass
    return "Chat Server is running..."

if __name__ == "__main__":
    socketio.run(app)
