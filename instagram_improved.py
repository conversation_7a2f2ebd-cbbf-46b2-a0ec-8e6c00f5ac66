#!/usr/bin/env python3
"""
Instagram Integration Service

This script provides a Flask web service that integrates with Instagram
to download and upload clips from direct messages.

HTTP Status Codes:
    400 Bad Request
    401 Unauthorized / Unauthenticated
    403 Forbidden / Banned
    404 Not Found
    500 Internal Server Error
"""

import os
import re
import sys
import json
import atexit
import logging
import datetime
from typing import Tuple

import requests
from instagrapi import Client
from instagrapi.types import DirectMessage
from signal import signal, SIGTERM
from multiprocessing import Manager
from multiprocessing.util import _exit_function
from concurrent.futures import ThreadPoolExecutor
from dankware import err, clr, get_duration
from flask import Flask, Response, render_template

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Change to script directory
os.chdir(os.path.dirname(__file__))
start_time = datetime.datetime.now()

def shutdown(_signal=None, _frame=None) -> None:
    """Handle graceful shutdown of the application.
    
    Args:
        _signal: Signal number (unused but required for signal handler)
        _frame: Current stack frame (unused but required for signal handler)
    """
    logger.warning(clr("  - SIGTERM Received!", 2))
    try:
        requests.post(
            server_webhook, 
            json={"content": f"```❌ dank.local-server #{os.getppid()}-{os.getpid()} shutting down...```"}, 
            timeout=3
        )
    except Exception as exc:
        logger.error(clr(f'  - [ERROR] server_webhook: {str(exc)}', 2))
    
    global running
    running = False
    executor.shutdown(wait=False, cancel_futures=True)
    logger.info(clr("  - Exiting...", 2))
    sys.exit(0)

atexit.unregister(_exit_function)
atexit.register(shutdown)
signal(SIGTERM, shutdown)

# Initialize global variables
running = True
manager = Manager()

# Load credentials safely
try:
    with open('./assets/credentials_2.json', 'r', encoding='utf-8') as f:
        credentials = json.load(f)
    
    # Extract configuration values
    server_webhook = credentials.get('server_webhook')
    server_error_webhook = credentials.get('server_error_webhook')
    instagram_username = credentials.get('instagram_username')
    instagram_password = credentials.get('instagram_password')
    
    # Validate required credentials
    if not all([server_webhook, server_error_webhook, instagram_username, instagram_password]):
        missing = [k for k, v in {
            'server_webhook': server_webhook,
            'server_error_webhook': server_error_webhook,
            'instagram_username': instagram_username,
            'instagram_password': instagram_password
        }.items() if not v]
        raise ValueError(f"Missing required credentials: {', '.join(missing)}")
        
except Exception as e:
    logger.error(f"Failed to load credentials: {str(e)}")
    sys.exit(1)

# Flask app configuration
logger.info(clr("  - Loading Flask..."))
app = Flask(__name__)

# Configure thread pool with a reasonable number of workers
# Using min(32, os.cpu_count() + 4) is a good practice for I/O bound tasks
executor = ThreadPoolExecutor(max_workers=min(8, os.cpu_count() or 4))

# Flask routes

@app.route('/', methods=['GET'])
def index() -> Tuple[str, int]:
    """Render the main index page.
    
    Returns:
        tuple: Rendered template and HTTP status code
    """
    return render_template('index.html'), 200

# Instagram client initialization

client = Client()
instagram_success = False
instagram_clips = manager.list()  # Shared list for multiprocessing

def initialize_instagram() -> bool:
    """Initialize the Instagram client and attempt to log in.
    
    Returns:
        bool: True if login was successful, False otherwise
    """
    global instagram_success, client
    
    try:
        if client.login(instagram_username, instagram_password):
            logger.info(clr("  - Instagram login successful!"))
            return True
        else:
            logger.error(clr("  - Instagram login failed!", 2))
            return False
    except Exception as exc:
        client = None
        error = err((type(exc), exc, exc.__traceback__), 'mini')
        try:
            requests.post(
                server_error_webhook, 
                json={"content": f"```<--- ⚠️ Instagram Login Failed ⚠️ --->\n\n{error}```"}, 
                timeout=3
            )
        except Exception as webhook_exc:
            logger.error(f"Failed to send error webhook: {str(webhook_exc)}")
        return False

# Attempt to log in to Instagram
instagram_success = initialize_instagram()

# Remove credentials from memory for security
del instagram_username, instagram_password

def instagram_uploader(last_message: DirectMessage) -> None:
    """Download and upload an Instagram clip from a direct message.
    
    Args:
        last_message: The DirectMessage object containing the clip to upload
    """
    thumbnail_path = None
    clip_path = None
    
    try:
        # Download thumbnail if available
        if last_message.clip.image_versions2:
            thumbnail_path = client.photo_download_by_url(
                last_message.clip.image_versions2['candidates'][0]['url']
            )
        
        # Download and upload the clip
        clip_path = client.clip_download(last_message.clip.pk)
        
        # Remove @mentions from caption for privacy
        clean_caption = re.sub(r'@[a-z0-9._]+', '', last_message.clip.caption_text)
        
        # Upload the clip
        _ = client.clip_upload(clip_path, clean_caption, thumbnail_path)
        
        # Track uploaded clip to avoid duplicates
        instagram_clips.append(last_message.clip.pk)
        
        logger.info(f"Successfully uploaded clip {last_message.clip.pk}")
        
    except Exception as exc:
        global instagram_success
        instagram_success = False
        logger.error(clr(f'  - Instagram upload failed: {str(exc)}', 2))
        
        # Log detailed error information
        error = err((type(exc), exc, exc.__traceback__), 'mini')
        try:
            requests.post(
                server_error_webhook, 
                json={"content": f"```<--- ⚠️ Instagram Upload Failed ⚠️ --->\n\n{error}```"}, 
                timeout=3
            )
        except Exception as webhook_exc:
            logger.error(f"Failed to send error webhook: {str(webhook_exc)}")
            
        # Attempt to logout on failure
        try:
            client.logout()
        except Exception:
            pass
    finally:
        # Clean up temporary files
        for path in [p for p in [thumbnail_path, clip_path] if p and os.path.exists(p)]:
            try:
                os.remove(path)
            except Exception as e:
                logger.warning(f"Failed to remove temporary file {path}: {str(e)}")

@app.route('/instagram-upload', methods=['GET'])
def instagram_upload() -> Tuple[Response, int]:
    """Handle Instagram clip upload requests.
    
    Returns:
        tuple: Response object and HTTP status code
    """
    if not instagram_success:
        return Response('Instagram login failed!', mimetype='text/plain'), 500
    
    try:
        # Instagram user ID for direct messages
        user_id = 340282366841710301244259516440940995898
        
        # Get the most recent direct message
        messages = client.direct_messages(user_id, 1)
        
        if not messages:
            return Response('No messages found!', mimetype='text/plain'), 200
        
        last_message = messages[0]
        
        # Check if the message contains a clip that hasn't been processed yet
        if (last_message.item_type == 'clip' and 
                last_message.clip.pk not in instagram_clips):
            # Process the upload in a separate thread
            executor.submit(instagram_uploader, last_message)
            return Response('Instagram upload initiated!', mimetype='text/plain'), 200
        
        return Response('Instagram upload skipped (no new clips)!', mimetype='text/plain'), 200
        
    except Exception as exc:
        logger.error(clr(f'  - Instagram upload failed: {str(exc)}', 2))
        
        # Log detailed error information
        error = err((type(exc), exc, exc.__traceback__), 'mini')
        try:
            requests.post(
                server_error_webhook, 
                json={"content": f"```<--- ⚠️ Instagram Upload Failed ⚠️ --->\n\n{error}```"}, 
                timeout=3
            )
        except Exception as webhook_exc:
            logger.error(f"Failed to send error webhook: {str(webhook_exc)}")
            
        return Response('Instagram upload failed!', mimetype='text/plain'), 500

@app.route('/shutdown', methods=['GET'])
def instagram_logout() -> Tuple[Response, int]:
    """Logout from Instagram and shutdown the service.
    
    Returns:
        tuple: Response object and HTTP status code
    """
    global instagram_success
    
    if instagram_success:
        try:
            client.logout()
            logger.info("Successfully logged out from Instagram")
        except Exception as e:
            logger.warning(f"Error during logout: {str(e)}")
        finally:
            instagram_success = False
        
        return Response('Logged out!', mimetype='text/plain'), 200
    
    return Response('Already logged out!', mimetype='text/plain'), 200

def send_startup_notification() -> None:
    """Send a notification that the server has started."""
    end_time = datetime.datetime.now()
    startup_duration = get_duration(start_time, end_time, interval='seconds')
    
    try:
        requests.post(
            server_webhook, 
            json={
                "content": f"```✅ dank.local-server online! "
                          f"#{os.getppid()}-{os.getpid()} "
                          f"took {startup_duration}s!```"
            }, 
            timeout=3
        )
    except Exception as exc:
        logger.error(clr(f'  - [ERROR] server_webhook: {str(exc)}', 2))

# Send startup notification
send_startup_notification()

if __name__ == "__main__":
    logger.info(clr("  - Starting Flask App..."))
    
    # Get port from environment variable or use default
    port = int(os.environ.get("PORT", 10000))
    
    # Start the Flask application
    app.run(
        host='0.0.0.0',
        port=port,
        threaded=True
        # Uncomment for HTTPS support
        # ssl_context='adhoc'
    )
