<!DOCTYPE html>
<html lang="en" >
<head>
  <meta charset="UTF-8">
  <title>dank.auth</title>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prefixfree/1.0.7/prefixfree.min.js"></script>
<link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon"/>
<link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon"/>

<link href='https://fonts.googleapis.com/css?family=Lato:100,300,400,700,400italic,700italic,900&subset=cyrillic-ext,latin,latin-ext' rel='stylesheet' type='text/css'/>
<link rel="stylesheet" href="{{ url_for('static', filename='cursor.css') }}"/>
<style>
   @import url("https://fonts.googleapis.com/css?family=Press+Start+2P");
   html, body {
     width: 100%;
     height: 100%;
     margin: 0;
   }
   
   * {
     font-family: "Press Start 2P", cursive;
     box-sizing: border-box;
   }
   
   #app {
     padding: 1rem;
     background: black;
     display: flex;
     height: 100%;
     justify-content: center;
     align-items: center;
     color: #54FE55;
     text-shadow: 0px 0px 10px;
     font-size: 6rem;
     flex-direction: column;
   }
   #app .txt {
     font-size: 1.8rem;
   }
   
   @keyframes blink {
     0% {
       opacity: 0;
     }
     49% {
       opacity: 0;
     }
     50% {
       opacity: 1;
     }
     100% {
       opacity: 1;
     }
   }
   .blink {
     animation-name: blink;
     animation-duration: 1s;
     animation-iteration-count: infinite;
   }
</style>
</head>
<body>
<!-- partial:index.partial.html -->
<div id="app">
   <div>200</div>
   <div class="txt">
      Access-Granted<span class="blink">_</span>
   </div>
</div>
<div id="output"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<div class="box"></div>
<!-- partial -->
<script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/2.1.3/jquery.min.js'></script>
<script src='https://cdnjs.cloudflare.com/ajax/libs/gsap/1.15.0/TweenMax.min.js'></script><script  src="{{ url_for('static', filename='cursor.js') }}"></script>
</body>
</html>
