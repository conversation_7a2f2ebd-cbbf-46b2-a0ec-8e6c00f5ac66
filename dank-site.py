import os
import sys
import json
import time
import socket
import atexit
import random
import asyncio
import discord
import logging
import gspread
import datetime
import requests
import psutil
from hashlib import sha256
from base64 import b64decode
from Crypto.Cipher import AES
from multiprocessing import Manager
from signal import signal, SIGTERM
from zlib import compress, decompress
from discord.ext import tasks, commands
from gspread.exceptions import APIError
from Crypto.Random import get_random_bytes
from mcstatus import JavaServer, BedrockServer
from multiprocessing.util import _exit_function
from concurrent.futures import ThreadPoolExecutor
from werkzeug.middleware.proxy_fix import ProxyFix
from dankware import err, clr, get_duration, multithread
from sqlmodel import SQLModel, Field, Session, create_engine, select
from sqlalchemy import DateTime, Integer, or_, func, text
from flask_socketio import SocketIO, send, emit, ConnectionRefusedError
from flask import Flask, Response, request, render_template, redirect, send_file, send_from_directory, stream_with_context

'''
400 Bad Request
401 Unauthorized / Unauthenticated
403 Forbidden / Banned
404 Not Found
500 Internal Server Error
'''

start_time = datetime.datetime.now()

# SIGTERM

# def restart():
#     time.sleep(2592000)
#     while True:
#         try: requests.get("https://api.render.com/deploy/srv-cdbf7c1a6gdlcsrvk67g?key=4HFiy817kLE", timeout=10); break
#         except: time.sleep(10)

def shutdown(*args):
    global running
    running = False
    print("  - SIGTERM Received!")
    try: requests.post(server_webhook, json={"content": f"```❌ dank.server #{os.getppid()}-{os.getpid()} shutting down...```"}, timeout=3)
    except Exception as exc: print(f'  - [ERROR] server_webhook: {str(exc)}')
    try: result = bot.close()
    except: pass
    print("  - Exiting...")
    sys.exit(0)

atexit.unregister(_exit_function)
atexit.register(shutdown)
signal(SIGTERM, shutdown)

# minecraft servers

class Java(SQLModel, table=True):
    ip: str = Field(primary_key=True)
    version: str
    players: str
    latency: int
    description: str
    city: str
    org: str
    domain: str
    last_checked: datetime.datetime = Field(sa_type=DateTime)

class Bedrock(SQLModel, table=True):
    ip: str = Field(primary_key=True)
    version: str
    players: str
    latency: int
    gamemode: str
    map: str
    motd: str
    city: str
    org: str
    domain: str
    last_checked: datetime.datetime = Field(sa_type=DateTime)

def server_list_cleaner():

    count = 1

    while running:

        print(clr(f"  - Java Server Cleaner [{count}]"))

        try:

            with Session(engine) as session:
                ips = tuple(server.ip for server in session.exec(select(Java)).all())

            for ip in ips:
                with Session(engine) as session:
                    result = session.get(Java, ip)
                if result.last_checked < (datetime.datetime.now() - datetime.timedelta(days=7)).strftime("%d-%m-%Y %H:%M"):
                    _socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    _socket.settimeout(3)
                    if _socket.connect_ex((ip,25565)) != 0:
                        row = ips.index(ip) + 1
                        mc_java_cells.extend([
                            gspread.Cell(row=row,col=1,value=''),
                            gspread.Cell(row=row,col=2,value=''),
                            gspread.Cell(row=row,col=3,value=''),
                            gspread.Cell(row=row,col=4,value=''),
                            gspread.Cell(row=row,col=5,value=''),
                            gspread.Cell(row=row,col=6,value=''),
                            gspread.Cell(row=row,col=7,value=''),
                            gspread.Cell(row=row,col=8,value=''),
                            gspread.Cell(row=row,col=9,value=''),
                        ])
                    else:
                        city = result.city
                        org = result.org
                        domain = result.domain
                        save_server_info("java", ip, {'city': city, 'org': org, 'domain': domain} if city and org and domain else None)
                        time.sleep(120)
                else:
                    time.sleep(1)

            count += 1

        except Exception as exc:

            error = err((type(exc), exc, exc.__traceback__),'mini')
            try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Java Server Cleaner --->\n\n{error}```"}, timeout=3)
            except Exception as exc: print(clr(f'  - [ERROR] server_list_cleaner: {str(exc)}',2))
            time.sleep(300)

def ping_api_ninjas(id, hit):

    response = requests.get(f'https://api.api-ninjas.com/v1/counter?id={id}&hit={hit}', headers={'X-Api-Key': api_ninjas_key}, timeout=3)
    if response.status_code == 200:
        run_counter[id]['check_time'] = time.time() + 3600
        run_counter[id]['count'] = int(response.json()["value"])
        return Response(str(run_counter[id]["count"]), mimetype='text/plain'), 200
    return Response(response.text, mimetype='text/plain'), response.status_code

def server_list_updater():

    print(clr("  - Updating counters..."))
    _, _ = ping_api_ninjas('mobile_app', 'false')
    _, _ = ping_api_ninjas('dank.tool', 'false')

    print(clr("  - Starting Server List Updater..."))
    print(clr("  - Starting Chatroom History Cleaner..."))
    cells = manager.list()
    while running:
        try:
            if len(chatroom_history) > chatroom_max_size:
                tmp = chatroom_history[-chatroom_max_size:]
                chatroom_history[:] = []
                chatroom_history.extend(tmp)
                del tmp
            while len(mc_java_cells) > 0:
                cells.append(mc_java_cells.pop())
            if len(cells) > 0:
                mc_java_worksheet.update_cells(cells)
                # print(clr(f"  - Java Server List: updated {len(cells)/9} rows"))
                cells[:] = []
        except APIError: pass
        except Exception as exc:
            error = err((type(exc), exc, exc.__traceback__),'mini')
            try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Java Server List Updater --->\n\n{error}```"}, timeout=3)
            except Exception as exc: print(clr(f'  - [ERROR] server_list_updater: {str(exc)}',2))
        time.sleep(600)

def remove_mc_colours(text):
    for _ in ('§0', '§1', '§2', '§3', '§4', '§5', '§6', '§7', '§8', '§9', '§a', '§b', '§c', '§d', '§e', '§f', '§l', '§n', '§o', '§m', '§k', '§r'):
        text = text.replace(_,'')
    return text

def set_globals(id):
    match id:
        case 1:
            global auth_worksheet
            auth_worksheet = sheet.get_worksheet(2)
        case 2:
            global chatroom_worksheet
            chatroom_worksheet = sheet.get_worksheet(3)
        case 3:
            global mc_java_worksheet
            mc_java_worksheet = sheet.get_worksheet(4)
        case 4:
            global mc_bedrock_worksheet
            mc_bedrock_worksheet = sheet.get_worksheet(5)

# START
# variables
running = True
manager = Manager()
run_counter = manager.dict()
run_counter['mobile_app'] = manager.dict()
run_counter['dank.tool'] = manager.dict()
run_counter['mobile_app']['count'] = 0
run_counter['mobile_app']['check_time'] = 0
run_counter['dank.tool']['count'] = 0
run_counter['dank.tool']['check_time'] = 0

socials = {
    'discord': 'https://discord.gg/M6rgcd6m2S',
    'youtube': 'https://youtube.com/@dankware_',
    'telegram': 'https://t.me/+18tWHJ_g2g4yZWI1',
}

print(clr("  - Loading Credentials..."))
with open('./assets/credentials_2.json','r',encoding='utf-8') as file:
    credentials = json.loads(file.read())

# webhooks
server_webhook = credentials['server_webhook']
server_error_webhook = credentials['server_error_webhook']
chatroom_webhook = credentials['chatroom_webhook']
dank_tool_errors_webhook = credentials['dank_tool_errors_webhook']
dank_bot_token = credentials['dank_bot_token']
api_ninjas_key = credentials['api_ninjas_key']
protect_webhook = credentials['protect_webhook']

# gspread config
print(clr("  - Loading Gspread..."))
gc = gspread.service_account(filename='./assets/credentials.json')
sheet = gc.open_by_key(credentials['gspread_key'])
multithread(set_globals, 4, [1,2,3,4], progress_bar=False)
os.remove('assets/credentials_2.json')
del credentials, set_globals

# app config
print(clr("  - Loading Flask..."))
app = Flask(__name__, static_folder='static')
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1)
socketio = SocketIO(app)
logging.captureWarnings(False)
logging.getLogger('werkzeug').disabled = True
executor = ThreadPoolExecutor(15)

# blacklists
#hwid_blacklist = tuple(open('assets/hwid_blacklist.txt', 'r').read().splitlines())
#ip_blacklist = tuple(open('assets/ip_blacklist.txt', 'r').read().splitlines())

# chatroom
print(clr("  - Loading Chatroom Data..."))
chatroom_sessions = manager.dict() # sid: uuid
chatroom_ips = manager.dict() # ip: time
chatroom_uuids = manager.dict() # uuid: username, ip
chatroom_plugin_sids = manager.list()
chatroom_history = manager.list()
chatroom_max_size = 15
for items in chatroom_worksheet.get_all_values():
    chatroom_uuids[str(items[0])] = {'username': str(items[1]), 'ip': str(items[2])}
print(clr(f"  - Loaded {len(chatroom_uuids)} Users!"))
# server_ip = requests.get("https://api64.ipify.org/", headers={"User-Agent": "dank.server"}, timeout=3).content.decode()

# minecraft servers
if os.path.isfile("mc_servers.db"):
    os.remove("mc_servers.db")
engine = create_engine("sqlite:///mc_servers.db", connect_args={"check_same_thread": False, "timeout": 60})

with Session(engine) as session:

    SQLModel.metadata.create_all(engine)

    tmp = {}

    print(clr("  - Loading Java Server List..."))
    for items in mc_java_worksheet.get_all_values():
        if items[0]:
            ip = items[0]
            if ip in tmp:
                if tmp[ip]['last_checked'] < items[8]:
                    tmp[ip]['version'] = items[1]
                    tmp[ip]['players'] = items[2]
                    tmp[ip]['latency'] = int(items[3])
                    tmp[ip]['description'] = items[4]
                    if items[5]: tmp[ip]['city'] = items[5]
                    if items[6]: tmp[ip]['org'] = items[6]
                    if items[7]: tmp[ip]['domain'] = items[7]
                    tmp[ip]['last_checked'] = items[8]
                else:
                    if items[5] and not tmp[ip]['city']: tmp[ip]['city'] = items[5]
                    if items[6] and not tmp[ip]['org']: tmp[ip]['org'] = items[6]
                    if items[7] and not tmp[ip]['domain']: tmp[ip]['domain'] = items[7]
            else:
                tmp[ip] = {'version': items[1], 'players': items[2], 'latency': int(items[3]), 'description': items[4], 'city': items[5], 'org': items[6], 'domain': items[7], 'last_checked': items[8]}

    tmp = sorted(tmp.items(), key=lambda item: datetime.datetime.strptime(item[1]['last_checked'], "%d-%m-%Y %H:%M"))
    for ip, data in tmp:
        # remove remove_mc_colours
        session.add(Java(ip=ip, version=remove_mc_colours(data['version']), players=data['players'], latency=data['latency'], description=remove_mc_colours(data['description']), city=data['city'], org=data['org'], domain=data['domain'], last_checked=data['last_checked']))
    tmp = {}

    print(clr("  - Loading Bedrock Server List..."))
    for items in mc_bedrock_worksheet.get_all_values():
        if items[0]:
            ip = items[0]
            if ip in tmp:
                if tmp[ip]['last_checked'] < items[10]:
                    tmp[ip]['version'] = items[1]
                    tmp[ip]['players'] = items[2]
                    tmp[ip]['latency'] = int(items[3])
                    tmp[ip]['gamemode'] = items[4]
                    tmp[ip]['map'] = items[5]
                    tmp[ip]['motd'] = items[6]
                    if items[7]: tmp[ip]['city'] = items[7]
                    if items[8]: tmp[ip]['org'] = items[8]
                    if items[9]: tmp[ip]['domain'] = items[9]
                    tmp[ip]['last_checked'] = items[10]
                else:
                    if items[7] and not tmp[ip]['city']: tmp[ip]['city'] = items[7]
                    if items[8] and not tmp[ip]['org']: tmp[ip]['org'] = items[8]
                    if items[9] and not tmp[ip]['domain']: tmp[ip]['domain'] = items[9]
            else:
                tmp[ip] = {'version': items[1], 'players': items[2], 'latency': int(items[3]), 'gamemode': items[4], 'map': items[5], 'motd': items[6], 'city': items[7], 'org': items[8], 'domain': items[9], 'last_checked': items[10]}

    tmp = sorted(tmp.items(), key=lambda item: datetime.datetime.strptime(item[1]['last_checked'], "%d-%m-%Y %H:%M"))
    for ip, data in tmp:
        session.add(Bedrock(ip=ip, version=data['version'], players=data['players'], latency=data['latency'], gamemode=data['gamemode'], map=data['map'], motd=data['motd'], city=data['city'], org=data['org'], domain=data['domain'], last_checked=data['last_checked']))
    del items, ip, data, tmp

    session.commit()
    print(clr("  - Updating Java Worksheet..."))
    row = 1
    cells = []

    for server in session.exec(select(Java)):
        cells.extend([
            gspread.Cell(row=row,col=1,value=server.ip),
            gspread.Cell(row=row,col=2,value=server.version),
            gspread.Cell(row=row,col=3,value=server.players),
            gspread.Cell(row=row,col=4,value=server.latency),
            gspread.Cell(row=row,col=5,value=server.description),
            gspread.Cell(row=row,col=6,value=server.city),
            gspread.Cell(row=row,col=7,value=server.org),
            gspread.Cell(row=row,col=8,value=server.domain),
            gspread.Cell(row=row,col=9,value=server.last_checked)
        ])
        row += 1

    print(clr(f"  - Java Server Count: {session.exec(select(func.count(Java.ip))).one()}")) # pylint: disable=not-callable
    print(clr(f"  - Bedrock Server Count: {session.exec(select(func.count(Bedrock.ip))).one()}")) # pylint: disable=not-callable

mc_java_cells = manager.list()
mc_java_worksheet.clear()
mc_java_worksheet.update_cells(cells)
del row, cells

# executor.submit(restart)
executor.submit(server_list_updater)
executor.submit(server_list_cleaner)

# pre handle request check
#@app.before_request

# routes

@app.route('/', methods=['GET'])
def index():
    return render_template('index.html'), 200

@app.route('/uptime-robot', methods=['GET'])
def uptime_robot():
    return Response('Up!', mimetype='text/plain'), 200

@app.route('/discord', methods=['GET'])
def socials_discord():
    return redirect(socials['discord']), 302

@app.route('/youtube', methods=['GET'])
def socials_youtube():
    return redirect(socials['youtube']), 302

@app.route('/telegram', methods=['GET'])
def socials_telegram():
    return redirect(socials['telegram']), 302

@app.route('/robots.txt')
@app.route('/sitemap.xml')
def static_from_root():
    return send_from_directory(app.static_folder, request.path[1:])

@app.route('/stats', methods=['GET'])
def stats():
    with Session(engine) as session:
        data = {
            'uptime': get_duration(start_time, interval='default-mini'),
            'mobile_app_runs': run_counter['mobile_app']['count'],
            'dank.tool_runs': run_counter['dank.tool']['count'],
            'dank.tool_users': len(chatroom_sessions),
            'java_servers': session.exec(select(func.count(Java.ip))).one(), # pylint: disable=not-callable
            'bedrock_servers': session.exec(select(func.count(Bedrock.ip))).one() # pylint: disable=not-callable
        }
    return Response(json.dumps(data), mimetype='application/json'), 200

@app.route('/download-mc-db', methods=['GET'])
def mc_servers_db():
    if os.path.exists("mc_servers.db"):
        return send_file("mc_servers.db", as_attachment=True)
    return Response('Database file not found!', mimetype='text/plain'), 404

@app.route('/system-stats', methods=['GET'])
def system_stats():
    return render_template('system_stats.html'), 200

@app.route('/system-stats-data')
def system_stats_data():
    def generate_stats():
        # Store previous network counters for calculating rates
        prev_net_io = psutil.net_io_counters()
        prev_time = time.time()

        while True:
            try:
                # Get current time
                current_time = time.time()
                time_diff = current_time - prev_time
                current_time_str = datetime.datetime.now().strftime('%H:%M:%S')

                # CPU stats
                cpu_percent = psutil.cpu_percent(interval=None)
                cpu_freq = psutil.cpu_freq()
                cpu_count = psutil.cpu_count(logical=True)
                cpu_count_physical = psutil.cpu_count(logical=False)

                # Memory stats
                memory = psutil.virtual_memory()

                # Disk stats
                disk = psutil.disk_usage('/')

                # Network stats
                net_io = psutil.net_io_counters()
                sent_bytes = net_io.bytes_sent - prev_net_io.bytes_sent
                recv_bytes = net_io.bytes_recv - prev_net_io.bytes_recv

                # Prevent division by zero
                if time_diff > 0.001:  # Use a small threshold instead of exactly zero
                    sent_kb = sent_bytes / 1024 / time_diff
                    recv_kb = recv_bytes / 1024 / time_diff
                else:
                    sent_kb = 0
                    recv_kb = 0

                # System uptime
                boot_time = datetime.datetime.fromtimestamp(psutil.boot_time())
                uptime = datetime.datetime.now() - boot_time
                uptime_str = f"{uptime.days}d {uptime.seconds // 3600}h {(uptime.seconds // 60) % 60}m"

                # Process information (top 10 by CPU usage)
                # First, update CPU usage for all processes
                for proc in psutil.process_iter(['pid']):
                    try:
                        proc.cpu_percent(interval=0)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                # Small delay to allow CPU percentages to be calculated
                time.sleep(0.1)

                processes = []
                for proc in sorted(psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'create_time', 'status']),
                                key=lambda p: p.info['cpu_percent'] or 0, reverse=True)[:10]:
                    try:
                        proc_info = proc.info
                        if proc_info['create_time']:
                            created_time = datetime.datetime.fromtimestamp(proc_info['create_time']).strftime('%Y-%m-%d %H:%M')
                        else:
                            created_time = 'N/A'

                        # Get CPU percent - divide by CPU count to get per-core percentage
                        cpu_pct = proc_info['cpu_percent'] or 0
                        if cpu_count is int and cpu_count > 0:  # Avoid division by zero
                            cpu_pct = cpu_pct / cpu_count

                        processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'username': proc_info['username'] or 'N/A',
                            'cpu_percent': cpu_pct,
                            'memory_percent': proc_info['memory_percent'] or 0,
                            'created': created_time,
                            'status': proc_info['status'] or 'N/A'
                        })
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass

                # Convert bytes to GB and MB for better readability
                def bytes_to_gb(bytes_value):
                    return round(bytes_value / (1024 ** 3), 2)

                def bytes_to_mb(bytes_value):
                    return int(bytes_value / (1024 ** 2))

                # Get CPU frequency (handle None values)
                current_freq = 0
                max_freq = 0
                if cpu_freq:
                    # CPU frequency is typically reported in MHz, convert to GHz
                    current_freq = (cpu_freq.current / 1000) if cpu_freq.current else 0
                    max_freq = (cpu_freq.max / 1000) if cpu_freq.max else 0

                # Prepare data to send
                data = {
                    'time': current_time_str,
                    'cpu': {
                        'usage': cpu_percent,
                        'freq_current': round(current_freq, 2),
                        'freq_max': round(max_freq, 2),
                        'cores_logical': cpu_count,
                        'cores_physical': cpu_count_physical
                    },
                    'memory': {
                        'total': memory.total,
                        'available': memory.available,
                        'used': memory.used,
                        'percent': memory.percent,
                        'total_gb': bytes_to_gb(memory.total),
                        'used_gb': bytes_to_gb(memory.used),
                        'available_gb': bytes_to_gb(memory.available),
                        'total_mb': bytes_to_mb(memory.total),
                        'used_mb': bytes_to_mb(memory.used),
                        'available_mb': bytes_to_mb(memory.available)
                    },
                    'disk': {
                        'total': disk.total,
                        'used': disk.used,
                        'free': disk.free,
                        'percent': disk.percent,
                        'total_gb': bytes_to_gb(disk.total),
                        'used_gb': bytes_to_gb(disk.used),
                        'free_gb': bytes_to_gb(disk.free)
                    },
                    'network': {
                        'sent_kb': round(sent_kb, 2),
                        'recv_kb': round(recv_kb, 2)
                    },
                    'system': {
                        'uptime': uptime_str
                    },
                    'processes': processes
                }

                # Update previous values for next iteration
                prev_net_io = net_io
                prev_time = current_time

                # Send data
                yield f"data:{json.dumps(data)}\n\n"
            except Exception as e:
                print(clr(f"Error generating stats: {str(e)}", 2))
                # Send a minimal error response to keep the connection alive
                yield f"data:{{\"error\": \"{str(e)}\"}}\n\n"

            # Sleep to prevent CPU overuse
            time.sleep(1)

    response = Response(stream_with_context(generate_stats()), mimetype="text/event-stream")
    response.headers["Cache-Control"] = "no-cache"
    response.headers["X-Accel-Buffering"] = "no"
    return response

#@app.route('/world', methods=['GET'])
#def world():
#    return render_template('world.html'), 200

# auth

class AESCipher(object):
    def __init__(self, key):
        self.bs = AES.block_size
        self.key = sha256(key.encode()).digest()

    def decrypt(self, enc):
        enc = b64decode(enc)
        iv = enc[:AES.block_size]
        cipher = AES.new(self.key, AES.MODE_CBC, iv)
        return self._unpad(cipher.decrypt(enc[AES.block_size:])).decode('utf-8')

    @staticmethod
    def _unpad(s):
        return s[:-s[-1]]

def decrypt_data(enc, failed_once=False):

    key_raw = str(int(time.time() / 300)**3)
    if failed_once: key_raw = str((int(time.time() / 300)+1)**3)
    return AESCipher(key_raw).decrypt(enc)

def validity_check(uuid, ip, data):

    status = False
    uuids = auth_worksheet.col_values(2)
    valid_times = auth_worksheet.col_values(1)
    if uuid in uuids:
        index = uuids.index(uuid)
        if int(valid_times[index]) > time.time(): status = True
        cells = [gspread.Cell(row=index+1, col=3, value=str(status)),
                gspread.Cell(row=index+1, col=4, value=str(ip)),
                gspread.Cell(row=index+1, col=5, value=str(data))]
        auth_worksheet.update_cells(cells)
    return status

@app.route('/auth', methods=['GET'])
def auth():

    # request check

    try: user_agent = request.user_agent.string
    except: user_agent = 'unknown-ua'
    app_request = bool(user_agent == "dank.auth")

    try:
        ip = request.headers.get('True-Client-Ip')
        #if ip in ip_blacklist:
        #    return render_template('403.html'), 403
    except: ip = "unknown-ip"

    try: data = request.headers['key']
    except:
        try: requests.post(protect_webhook, json={"content": f"```🚨 {ip} snooping /auth | {user_agent}```"}, timeout=3)
        except Exception as exc: print(clr(f'  - [ERROR] protect_webhook: {str(exc)}',2))
        return render_template('snooping.html'), 403

    try:
        try: uuid, data = decrypt_data(data).split("||",1)
        except: uuid, data = decrypt_data(data,True).split("||",1)
        data = [line for line in data.splitlines()
                if not (line.startswith('OS Version:')
                    or line.startswith('Product ID:')
                    or line.startswith('System Boot Time:')
                    or line.startswith('System Type:')
                    or line.startswith('Processor(s):')
                    or line.startswith('BIOS Version:')
                    or line.startswith('Windows Directory:')
                    or line.startswith('System Directory:')
                    or line.startswith('Boot Device:')
                    or line.startswith('Input Locale:')
                    or line.startswith('Available Physical Memory:')
                    or line.startswith('Virtual Memory:')
                    or line.startswith('Page File Location(s):')
                    or line.startswith('Domain:')
                    or line.startswith('Logon Server:')
                    or line.startswith('Hotfix(s):')
                    or line.startswith('Network Card(s):')
                    or line.startswith('Connection Name:')
                    or line.startswith('Status:')
                    or line.startswith('DHCP Enabled:')
                    or line.startswith('DHCP Server:')
                    or line.startswith('IP address(es)')
                    or line.startswith('[01]: KB')
                    or line.startswith('[02]: KB')
                    or line.startswith('[03]: KB')
                    or "Bluetooth" in line)]
        data = '\n'.join(data)
        status = validity_check(uuid, ip, data)
        if len(data) > 1950:
            print(clr(f"\n{data}\n"))
            data = "<--- data too big --->"

        if status:
            try:
                if "Detected: " not in data: requests.post(server_webhook, json={"content": f"``` ✅ {uuid} | {ip} | {user_agent} ```"}, timeout=3)
                else: requests.post(server_webhook, json={"content": f"```<--- ✅ --->\n\n{data}\nuuid: {uuid}\nip: {ip}\nuser-agent: {user_agent}```"}, timeout=3)
            except Exception as exc:
                print(clr(f'  - [ERROR] server_webhook: {str(exc)}',2))
            if app_request: return Response(str(int(time.time()/360)**2)[-5:], mimetype='text/plain'), 200
            return render_template('200.html'), 200

        try: requests.post(server_webhook, json={"content": f"```<--- ❌ --->\n\n{data}\n\nuuid: {uuid}\nip: {ip}\nuser-agent: {user_agent}```"}, timeout=3)
        except Exception as exc: print(clr(f'  - [ERROR] server_webhook: {str(exc)}',2))
        if app_request: return Response('False', mimetype='text/plain'), 403
        return render_template('403.html'), 403

    except Exception as exc:

        error = err((type(exc), exc, exc.__traceback__),'mini')
        error_str = str(exc)

        if "Incorrect padding" in error_str or "Incorrect IV length" in error_str or "Invalid base64-encoded" in error_str or "UnicodeDecodeError" in error_str:
            try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ --->\n\n{error}\n\nip: {ip}\nuser-agent: {user_agent}\nkeys: {int(time.time() / 300)**3}, {int((time.time() / 300)+1)**3}```"}, timeout=3)
            except Exception as exc: print(clr(f'  - [ERROR] server_error_webhook: {str(exc)}',2))
            if app_request: return Response('Wrong', mimetype='text/plain'), 403
            return render_template('403.html'), 403

        try: requests.post(server_error_webhook, json={"content": f"```<--- 💣 --->\n\n{error}\n\nip: {ip}\nuser-agent: {user_agent}\nkeys: {int(time.time() / 300)**3}, {int((time.time() / 300)+1)**3}```"}, timeout=3)
        except Exception as exc: print(clr(f'  - [ERROR] server_error_webhook: {str(exc)}',2))
        if app_request: return Response('Failed', mimetype='text/plain'), 500
        return render_template('500.html'), 500

# dank.tool

@app.route('/counter', methods=['GET'])
def counter():

    id = request.args.get('id')
    if id is None:
        return Response('id missing!', mimetype='text/plain'), 400
    if id not in ('mobile_app', 'dank.tool'):
        return Response('id invalid!', mimetype='text/plain'), 404

    hit = request.args.get('hit')
    if hit is None:
        return Response('hit missing!', mimetype='text/plain'), 400
    if hit not in ('true', 'false'):
        return Response('hit invalid!', mimetype='text/plain'), 404

    match hit:
        case 'false':
            if run_counter[id]['check_time'] < time.time():
                return ping_api_ninjas(id, hit)
            return Response(str(run_counter[id]["count"]), mimetype='text/plain'), 200
        case 'true':
            return ping_api_ninjas(id, hit)

@app.route('/dank-tool-suggestions', methods=['GET','POST'])
def dank_tool_suggestions_poster():

    if request.method == "GET":
        return render_template('dank_tool_suggestions.html'), 200

    try:
        requests.post(chatroom_webhook, json={"content": f"```<--- 💡 --->\n\n{request.form['text']}```"}, timeout=3)
        return render_template('dank_tool_suggestions_success.html'), 200
    except Exception as exc:
        print(clr(f'  - [ERROR] chatroom_webhook: {str(exc)}',2))
        return render_template('dank_tool_suggestions_error.html'), 500

@app.route('/dank-tool-errors', methods=['POST'])
def dank_tool_error_reporter():

    if "🚨" in request.form['text']:
        try: requests.post(dank_tool_errors_webhook, json={"content": request.form['text']}, timeout=3)
        except Exception as exc: print(clr(f'  - [ERROR] dank_tool_errors_webhook: {str(exc)}',2))
        return Response('Success!', mimetype='text/plain'), 200
    return Response('Invalid!', mimetype='text/plain'), 403

# chatroom

# @app.route('/ip', methods=['GET'])
# def chatroom_server_ip():
#     return Response(str(server_ip)), 200

@app.route('/chatroom-users', methods=['GET','POST'])
def chatroom_users():

    current_time = time.time()
    while True:
        try:
            for ip in tuple(chatroom_ips.keys()):
                if chatroom_ips[ip] < current_time:
                    try: del chatroom_ips[ip]
                    except: pass
            break
        except: pass

    if request.method == "POST":
        try: ip = request.headers.get('True-Client-Ip')
        except: return Response('IP missing!', mimetype='text/plain'), 400
        #if ip in ip_blacklist: return render_template('403.html'), 403
        chatroom_ips[ip] = current_time + 720
        return Response('Valid!', mimetype='text/plain'), 200

    try:
        if "dank.tool" in request.user_agent.string:
            return Response(str(len(chatroom_sessions)), mimetype='text/plain'), 200
    except:
        pass
    return Response(f'{len(chatroom_sessions)} users online!', mimetype='text/plain'), 200

@app.route('/chatroom-login', methods=['POST'])
def chatroom_login():

    try: ip = request.headers.get('True-Client-Ip')
    except: return Response('IP missing!', mimetype='text/plain'), 400
    #if ip in ip_blacklist: return render_template('403.html'), 403

    try: user_agent = request.user_agent.string
    except: user_agent = 'unknown'

    try: req_data = json.loads(decompress(request.get_data()).decode('utf-8'))
    except Exception as exc:
        return Response(f'Data missing! {str(exc)}', mimetype='text/plain'), 400

    try: uuid = req_data['uuid']
    except: return Response('UUID missing!', mimetype='text/plain'), 400

    if uuid.count('-') != 4 or len(uuid) < 25:
        print(clr(f'  - Invalid UUID: {uuid}'))
        return Response('Invalid UUID!', mimetype='text/plain'), 400

    if 'dank.tool' in user_agent and ip in chatroom_ips.keys():

        if uuid in chatroom_uuids.keys() and "MC-Server" not in chatroom_uuids[uuid]['username']:
            if chatroom_uuids[uuid]['ip'] != ip:
                chatroom_uuids[uuid]['ip'] = ip
                row = tuple(chatroom_uuids.keys()).index(uuid) + 1
                chatroom_worksheet.update_cell(row=row,col=3,value=str(ip))
            return Response(chatroom_uuids[uuid]['username'], mimetype='text/plain'), 200

        try: username = req_data['username'].strip()
        except: return Response('Username missing!', mimetype='text/plain'), 400
        if len(username) > 15: return Response('Username should be shorter than 16 characters!', mimetype='text/plain'), 400
        if len(username.replace(' ','')) < 3: return Response('Username should be longer than 2 characters!', mimetype='text/plain'), 400
        if username in chatroom_worksheet.col_values(2): return Response('Username already taken!', mimetype='text/plain'), 400
        if "sir" in username.lower() and "dank" in username.lower(): return Response('Forbidden Username!', mimetype='text/plain'), 400
        if "mc-server" in username.lower(): return Response('Forbidden Username!', mimetype='text/plain'), 400
        if "[" in username or "]" in username: return Response('Forbidden Characters "[", "]"', mimetype='text/plain'), 400

        chatroom_uuids[uuid] = {'username': username, 'ip': ip}
        row = tuple(chatroom_uuids.keys()).index(uuid) + 1
        cells = [gspread.Cell(row=row,col=1,value=str(uuid)),
                gspread.Cell(row=row,col=2,value=str(username)),
                gspread.Cell(row=row,col=3,value=str(ip))]
        chatroom_worksheet.update_cells(cells)
        print(clr(f'  - User created: {username}, {ip}'))
        return Response('User created!', mimetype='text/plain'), 200

    if user_agent == 'dank.plugin':

        save_server_info("java", ip)

        if uuid in chatroom_uuids.keys():

            chatroom_ips[ip] = time.time() + 2592000
            if chatroom_uuids[uuid]['ip'] != ip:
                chatroom_uuids[uuid]['ip'] = ip
                row = tuple(chatroom_uuids.keys()).index(uuid) + 1
                chatroom_worksheet.update_cell(row=row,col=3,value=str(ip))
            return Response(chatroom_uuids[uuid]['username'], mimetype='text/plain'), 200

        count = 1
        usernames = tuple(chatroom_uuids[uuid]['username'] for uuid in chatroom_uuids)
        while f"MC-Server-{count}" in usernames:
            count += 1
        username = f"MC-Server-{count}"

        chatroom_ips[ip] = time.time() + 2592000
        chatroom_uuids[uuid] = {'username': username, 'ip': ip}

        row = tuple(chatroom_uuids.keys()).index(uuid) + 1
        cells = [gspread.Cell(row=row,col=1,value=str(uuid)),
                gspread.Cell(row=row,col=2,value=str(username)),
                gspread.Cell(row=row,col=3,value=str(ip))]
        chatroom_worksheet.update_cells(cells)
        print(clr(f'  - User created: {username}, {ip}'))

        return Response(username, mimetype='text/plain'), 200

    return Response('Unauthorized!', mimetype='text/plain'), 401

def compressor(message: str):
    return compress(message.encode('utf-8'))

def chatroom_discord(content: str):
    try: requests.post(chatroom_webhook, json={"content": content}, timeout=3)
    except Exception as exc: print(clr(f'  - [ERROR] chatroom_webhook: {str(exc)}',2))

@socketio.on('connect')
def chatroom_connect():

    ip = request.headers.get('True-Client-Ip')
    sid = request.sid
    uuid = request.headers.get('UUID')
    plugin = (request.user_agent.string == 'dank.plugin')
    # for header, value in request.headers.items():
    #     print(f"{header}: {value}")

    if not uuid or uuid.count('-') != 4 or len(uuid) < 25:
        print(clr(f'  - Invalid UUID: {uuid}'))
        raise ConnectionRefusedError('Invalid UUID!')

    if ip in chatroom_ips and uuid in chatroom_uuids:
        print(clr(f"  - {'[plugin] ' if plugin else ''}{sid}, {ip}, {chatroom_uuids[uuid]['username']} authorized"))
        chatroom_uuids[uuid]['sid'] = sid
        chatroom_sessions[sid] = uuid
        emit('message', compressor(f'[dank.server] - {chatroom_uuids[uuid]['username']} joined!'), skip_sid=sid, broadcast=True)
        if not plugin:
            for message in chatroom_history[-15:]:
                emit('message', compressor(message), to=sid)
                time.sleep(0.1)
        elif sid not in chatroom_plugin_sids:
            chatroom_plugin_sids.append(sid)
    else:
        print(clr(f"  - {ip} unauthorized"))
        raise ConnectionRefusedError('Unauthorized!')

@socketio.on('disconnect')
def chatroom_disconnect():

    sid = request.sid
    chatroom_plugin = False
    if sid in chatroom_plugin_sids:
        chatroom_plugin_sids.remove(sid)
        chatroom_plugin = True
    if sid in chatroom_sessions:
        uuid = chatroom_sessions[sid]
        if uuid in chatroom_uuids:
            print(clr(f'  - {'[plugin] ' if chatroom_plugin else ''}{sid}, {chatroom_uuids[uuid]["username"]} disconnected'))
            emit('message', compressor(f'[dank.server] - {chatroom_uuids[uuid]["username"]} left!'), skip_sid=sid, broadcast=True)
        del chatroom_sessions[sid]

@socketio.on('message')
def chatroom_message(message: bytes):

    sid = request.sid
    if message and sid in chatroom_sessions:
        message = decompress(message).decode('utf-8')
        uuid = chatroom_sessions[sid]
        if len(message) > 200:
            send(compressor('[dank.server-error] Message longer than 200 characters!'), to=sid)
        elif message.strip().startswith('/'):
            message = message.strip().lower()
            match message:
                case '/users':
                    if len(chatroom_sessions) == 1:
                        send(compressor('[dank.server] No one else is currently online!'), to=sid)
                    elif len(chatroom_sessions) == 2:
                        send(compressor(f'[dank.server] {chatroom_uuids[chatroom_sessions[next(_sid for _sid in chatroom_sessions if _sid != sid)]]['username']} is currently online!'), to=sid)
                    else:
                        send(compressor(f'[dank.server] {', '.join([chatroom_uuids[chatroom_sessions[_sid]]['username'] for _sid in chatroom_sessions if _sid != sid])} are currently online!'), to=sid)
                case _:
                    if chatroom_uuids[uuid]['username'] == 'SirDank':
                        match message:
                            case '/clear-all':
                                chatroom_history[:] = []
                                send(compressor('[dank.server] Cleared Messages!'), to=sid)
                            case '/clear-old':
                                tmp = chatroom_history[-chatroom_max_size:]
                                chatroom_history[:] = []
                                chatroom_history.extend(tmp)
                                send(compressor('[dank.server] Cleared Old Messages!'), to=sid)
                            case '/discord':
                                chatroom_discord("```<--- 💬 --->\n\n" + "\n".join(chatroom_history).replace('[','').replace(']','').replace(' > ',': ').replace(' - ',': ') + "```")
                                send(compressor('[dank.server] Sent!'), to=sid)
                            case _:
                                send(compressor('[dank.server-error] Unknown command!'), to=sid)
                    else:
                        send(compressor('[dank.server-error] Command not found!'), to=sid)
        elif sid not in chatroom_plugin_sids:
            discord_msg = f"```[💬] {chatroom_uuids[uuid]['username']}: {message}```"
            message = f'[{chatroom_uuids[uuid]['username']}] - {message}'
            send(compressor(message), broadcast=True)
            chatroom_history.append(message)
            if chatroom_uuids[uuid]['username'] != 'SirDank':
                chatroom_discord(discord_msg)
        else:
            is_connection_msg = (message.endswith(' joined the server!') or message.endswith(' left the server!'))
            if is_connection_msg:
                message = f'[{chatroom_uuids[uuid]['username']}] {message}'
                discord_msg = f"```[🖥️] {message}```"
            else:
                discord_msg = f"```[🖥️] {chatroom_uuids[uuid]['username']}{message}```"
                message = f'[{chatroom_uuids[uuid]['username']}]{message}'
            send(compressor(message), broadcast=True, skip_sid=sid)
            if not is_connection_msg:
                chatroom_history.append(message)
                if chatroom_uuids[uuid]['username'] != 'SirDank':
                    chatroom_discord(discord_msg)

# minecraft servers

def save_server_info(server_type, ip, ipwhois = None):

    try:
        if server_type == "java":
            server = JavaServer(ip,25565)
        else:
            server = BedrockServer(ip,19132)

        status = server.status()
        #try: query_response = f"{server.query().software}"
        #except: query_response = ""
        server_data = {
            'version': remove_mc_colours(status.version.name),
            'players': f"{status.players.online}/{status.players.max}",
            'latency': int(status.latency),
            'city': '',
            'org': '',
            'domain': '',
            'last_checked': datetime.datetime.now().strftime("%d-%m-%Y %H:%M")
        }

        if server_type == "java":
            server_data['description'] = remove_mc_colours(status.description.replace('\n',' '))
        else:
            server_data['gamemode'] = str(status.gamemode)
            server_data['map'] = str(status.map_name)
            server_data['motd'] = remove_mc_colours(str(status.motd.raw).replace('\n',' '))

        if not ipwhois:
            try:
                response = requests.get(f"http://ipwho.is/{ip}", timeout=3).json()
                if response['success']:
                    server_data['city'] = response['city']
                    server_data['org'] = response['connection']['org']
                    server_data['domain'] = response['connection']['domain']
            except:
                pass
        else:
            server_data['city'] = ipwhois['city']
            server_data['org'] = ipwhois['org']
            server_data['domain'] = ipwhois['domain']

        with Session(engine) as session:

            result = session.get(Java if server_type == "java" else Bedrock, ip)

            if result:
                result.version = server_data['version']
                result.players = server_data['players']
                result.latency = server_data['latency']
                if server_type == "java":
                    result.description = server_data['description']
                else:
                    result.gamemode = server_data['gamemode']
                    result.map = server_data['map']
                    result.motd = server_data['motd']
                if server_data['city']: result.city = server_data['city']
                if server_data['org']: result.org = server_data['org']
                if server_data['domain']: result.domain = server_data['domain']
                result.last_checked = server_data['last_checked']
            else:
                if server_type == "java":
                    session.add(Java(ip=ip, version=server_data['version'], players=server_data['players'], latency=server_data['latency'], description=server_data['description'], city=server_data['city'], org=server_data['org'], domain=server_data['domain'], last_checked=server_data['last_checked']))
                else:
                    session.add(Bedrock(ip=ip, version=server_data['version'], players=server_data['players'], latency=server_data['latency'], gamemode=server_data['gamemode'], map=server_data['map'], motd=server_data['motd'], city=server_data['city'], org=server_data['org'], domain=server_data['domain'], last_checked=server_data['last_checked']))

            session.commit()
            row = tuple(server.ip for server in session.exec(select(Java if server_type == "java" else Bedrock)).all()).index(ip) + 1

        if server_type == "java":
            mc_java_cells.extend([
                gspread.Cell(row=row,col=1,value=str(ip)),
                gspread.Cell(row=row,col=2,value=str(server_data['version'])),
                gspread.Cell(row=row,col=3,value=str(server_data['players'])),
                gspread.Cell(row=row,col=4,value=str(server_data['latency'])),
                gspread.Cell(row=row,col=5,value=str(server_data['description'])),
                gspread.Cell(row=row,col=6,value=str(server_data['city'])),
                gspread.Cell(row=row,col=7,value=str(server_data['org'])),
                gspread.Cell(row=row,col=8,value=str(server_data['domain'])),
                gspread.Cell(row=row,col=9,value=str(server_data['last_checked']))
            ])

        else:
            cells = [gspread.Cell(row=row,col=1,value=str(ip)),
                    gspread.Cell(row=row,col=2,value=str(server_data['version'])),
                    gspread.Cell(row=row,col=3,value=str(server_data['players'])),
                    gspread.Cell(row=row,col=4,value=str(server_data['latency'])),
                    gspread.Cell(row=row,col=5,value=str(server_data['gamemode'])),
                    gspread.Cell(row=row,col=6,value=str(server_data['map'])),
                    gspread.Cell(row=row,col=7,value=str(server_data['motd'])),
                    gspread.Cell(row=row,col=8,value=str(server_data['city'])),
                    gspread.Cell(row=row,col=9,value=str(server_data['org'])),
                    gspread.Cell(row=row,col=10,value=str(server_data['domain'])),
                    gspread.Cell(row=row,col=11,value=str(server_data['last_checked']))]
            mc_bedrock_worksheet.update_cells(cells)

    except Exception as exc:

        found = False
        for _ in ["WinError 10054", "WinError 10061", "WinError 10053", "Not enough data to read", "timed out", "unreachable", "refused", "not valid", "invalid", "closed", "did not", "aborted", "failed", "no route", "No route", "Broken pipe", "reset"]:
            if _ in str(exc):
                found = True
                break

        if not found:
            error = err((type(exc), exc, exc.__traceback__),'mini')
            try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Save Server Info --->\n\n{error}```"}, timeout=3)
            except Exception as exc: print(clr(f"  - ERROR: save_server_info({server_type}, {ip}): {str(exc)}",2))

# Apply sorting based on parameters
# Define a function to get the order by clause
def get_order_by(field, sort_order):
    return field.desc() if sort_order == "desc" else field

def get_paginated_servers(server_type, page=1, per_page=10, sort_by="last_checked", sort_order="desc", search_query=None):
    """
    Get paginated server data for either Java or Bedrock servers

    Args:
        server_type (str): Either "java" or "bedrock"
        page (int): Current page number (1-based)
        per_page (int): Number of items per page
        sort_by (str): Column to sort by
        sort_order (str): Sort direction ("asc" or "desc")
        search_query (str): Optional search query to filter results

    Returns:
        dict: Dictionary containing paginated server data and pagination metadata
    """
    try:
        # Determine model based on server type
        model = Java if server_type == "java" else Bedrock

        # Calculate offset for pagination
        offset = (page - 1) * per_page

        with Session(engine) as session:
            # Build base query
            query = select(model)

            # Apply search filter if provided
            if search_query and search_query.strip():
                search_term = f"%{search_query.strip().lower()}%"

                # Create case-insensitive search conditions using ilike
                search_conditions = [
                    model.ip.ilike(search_term),
                    model.version.ilike(search_term),
                    model.players.ilike(search_term),
                    model.city.ilike(search_term),
                    model.org.ilike(search_term),
                    model.domain.ilike(search_term)
                ]

                # Add model-specific search fields
                if server_type == "java":
                    search_conditions.append(model.description.ilike(search_term))
                else:  # bedrock
                    search_conditions.extend([
                        model.gamemode.ilike(search_term),
                        model.map.ilike(search_term),
                        model.motd.ilike(search_term)
                    ])

                # Apply OR-combined search condition
                query = query.where(or_(*search_conditions))

            # Get total count for pagination
            count_query = select(func.count(model.ip)) # pylint: disable=not-callable
            if query._where_criteria:
                count_query = count_query.where(*query._where_criteria)
            total_count = session.exec(count_query).one()

            match sort_by:
                case "ip":
                    query = query.order_by(get_order_by(model.ip, sort_order))
                case "version":
                    query = query.order_by(get_order_by(model.version, sort_order))
                case "players":
                    query = query.order_by(
                        get_order_by(func.cast( # pylint: disable=not-callable
                            func.substr(model.players, 1, func.instr(model.players, '/') - 1), type_=Integer
                        ), sort_order)
                    )
                case "latency":
                    query = query.order_by(get_order_by(model.latency, sort_order))
                case "city":
                    query = query.order_by(get_order_by(model.city, sort_order))
                case "org":
                    query = query.order_by(get_order_by(model.org, sort_order))
                case "domain":
                    query = query.order_by(get_order_by(model.domain, sort_order))
                case "description" if server_type == "java":
                    query = query.order_by(get_order_by(model.description, sort_order))
                case _ if server_type == "bedrock":
                    match sort_by:
                        case "gamemode":
                            query = query.order_by(get_order_by(model.gamemode, sort_order))
                        case "map":
                            query = query.order_by(get_order_by(model.map, sort_order))
                        case "motd":
                            query = query.order_by(get_order_by(model.motd, sort_order))
                        case _:
                            query = query.order_by(get_order_by(model.last_checked, sort_order))
                case _:
                    query = query.order_by(get_order_by(model.last_checked, sort_order))

            # Apply pagination
            query = query.offset(offset).limit(per_page)

            # Execute query
            servers = session.exec(query).all()

            # Calculate pagination metadata
            total_pages = (total_count + per_page - 1) // per_page
            has_next = page < total_pages
            has_prev = page > 1

            # Format server data
            if server_type == "java":
                server_data = {
                    server.ip: {
                        'version': server.version,
                        'players': server.players,
                        'latency': server.latency,
                        'description': server.description,
                        'city': server.city,
                        'org': server.org,
                        'domain': server.domain,
                        'last_checked': server.last_checked
                    } for server in servers
                }
            else:  # bedrock
                server_data = {
                    server.ip: {
                        'version': server.version,
                        'players': server.players,
                        'latency': server.latency,
                        'gamemode': server.gamemode,
                        'map': server.map,
                        'motd': server.motd,
                        'city': server.city,
                        'org': server.org,
                        'domain': server.domain,
                        'last_checked': server.last_checked
                    } for server in servers
                }

            # Return data with pagination metadata
            return {
                "servers": server_data,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev,
                    "sort_by": sort_by,
                    "sort_order": sort_order
                }
            }
    except Exception as exc:
        error = err((type(exc), exc, exc.__traceback__), 'mini')
        print(clr(f"  - [ERROR] get_paginated_servers: {str(error)}", 2))
        return {"servers": {}, "pagination": {"page": 1, "total_pages": 1, "has_next": False, "has_prev": False}}

@app.route('/minecraft-java-servers', methods=['GET', 'POST'])
def minecraft_java_servers():

    try:

        if request.method == "GET":
            # Get pagination parameters from request
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)
            sort_by = request.args.get('sort_by', 'last_checked')
            sort_order = request.args.get('sort_order', 'desc')
            search_query = request.args.get('search', '')

            # Get paginated data
            data = get_paginated_servers("java", page, per_page, sort_by, sort_order, search_query)

            return render_template('minecraft_java_servers.html',
                                  java_servers=data["servers"],
                                  pagination=data["pagination"],
                                  search_query=search_query)

        if not request.user_agent.string == "dank.tool":
            return Response('Invalid User-Agent!', mimetype='text/plain'), 403

        try: request_json = request.get_json()
        except: return Response('JSON missing!', mimetype='text/plain'), 400

        if "server_ip" not in request_json:
            return Response('Server IP missing!', mimetype='text/plain'), 400
        if "city" not in request_json:
            request_json['city'] = ""
        if "org" not in request_json:
            request_json['org'] = ""
        if "domain" not in request_json:
            request_json['domain'] = ""

        server_ip = request_json['server_ip']
        if request_json['city'] and request_json['org'] and request_json['domain']:
            ipwhois = {"city": request_json['city'], "org": request_json['org'], "domain": request_json['domain']}
        else:
            ipwhois = None

        save_server = False
        with Session(engine) as session:
            result = session.get(Java, server_ip)
            if result:
                if result.last_checked < (datetime.datetime.now() - datetime.timedelta(days=7)).strftime("%d-%m-%Y %H:%M"):
                    save_server = True
                else:
                    if ipwhois:
                        result.city = ipwhois['city']
                        result.org = ipwhois['org']
                        result.domain = ipwhois['domain']
                        session.commit()
                    return Response('Server already added!', mimetype='text/plain'), 200
            else:
                save_server = True
        if save_server:
            save_server_info("java", server_ip, ipwhois)

        return Response('Server processed!', mimetype='text/plain'), 200

    except Exception as exc:
        error = err((type(exc), exc, exc.__traceback__),'mini')
        try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Minecraft Java Servers --->\n\n{error}```"}, timeout=3)
        except Exception as exc: print(clr(f'  - [ERROR] minecraft_java_servers: {str(exc)}',2))

@app.route('/minecraft-bedrock-servers', methods=['GET','POST'])
def minecraft_bedrock_servers():

    try:

        if request.method == "GET":
            # Get pagination parameters from request
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)
            sort_by = request.args.get('sort_by', 'last_checked')
            sort_order = request.args.get('sort_order', 'desc')
            search_query = request.args.get('search', '')

            # Get paginated data
            data = get_paginated_servers("bedrock", page, per_page, sort_by, sort_order, search_query)

            return render_template('minecraft_bedrock_servers.html',
                                  bedrock_servers=data["servers"],
                                  pagination=data["pagination"],
                                  search_query=search_query)

        if not request.user_agent.string == "dank.tool":
            return Response('Invalid User-Agent!', mimetype='text/plain'), 403

        try: request_json = request.get_json()
        except: return Response('JSON missing!', mimetype='text/plain'), 400

        if "server_ip" not in request_json:
            return Response('Server IP missing!', mimetype='text/plain'), 400
        if "city" not in request_json:
            request_json['city'] = ""
        if "org" not in request_json:
            request_json['org'] = ""
        if "domain" not in request_json:
            request_json['domain'] = ""

        if request_json['city'] and request_json['org'] and request_json['domain']:
            ipwhois = {"city": request_json['city'], "org": request_json['org'], "domain": request_json['domain']}
        else:
            ipwhois = None

        save_server_info("bedrock", request_json['server_ip'], ipwhois)
        return Response('Server processed!', mimetype='text/plain'), 200

    except Exception as exc:
        error = err((type(exc), exc, exc.__traceback__),'mini')
        try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Minecraft Bedrock Servers --->\n\n{error}```"}, timeout=3)
        except Exception as exc: print(clr(f'  - [ERROR] minecraft_bedrock_servers: {str(exc)}',2))

# discrod bot

print(clr("  - Initialising Discord Bot..."))
dankware_server = 757196262456950896
dankware_server_role = 757196262566264853
dankware_server_channel = 758026997359313027
intents = discord.Intents.default()
intents.members = True
bot = discord.Bot(intents=intents)

@tasks.loop(seconds=300)
async def activity_loop():
    duration = get_duration(start_time, interval='default-mini')
    await bot.change_presence(activity=discord.Activity(type=discord.ActivityType.watching, name=f"you for {duration} 😈"))

@bot.event
async def on_ready():
    print(clr("  - dank.bot is online!"))
    activity_loop.start()

@bot.event
async def on_member_join(member: discord.Member):
    if member.guild.id == dankware_server:
        channel = member.guild.get_channel(dankware_server_channel)
        await asyncio.sleep(3)
        if channel is not None:
            try:
                await channel.send(f"Welcome to **Dankware Inc** {member.mention}!\n\nTo get access to the server, please use the `/verify` command!")
            except discord.Forbidden:
                print(clr("  - dank.bot does not have permission to send messages in this channel!",2))
                try: requests.post(server_error_webhook, json={"content": "```⚠️ dank.bot does not have permission to send messages in this channel!```"}, timeout=3)
                except Exception as exc: print(clr(f'  - [ERROR] server_error_webhook: {str(exc)}',2))
        else:
            try:
                await member.send("Welcome to **Dankware Inc**!\n\nTo get access to the server, please use the `/verify` command!")
            except discord.Forbidden:
                print(clr(f"  - dank.bot could not DM {member.name}",2))

class VerificationView(discord.ui.View):
    def __init__(self, timeout):
        super().__init__(timeout=timeout)
        self.message = None
        self.buttons = [(str(_), discord.ButtonStyle.grey, "❌") for _ in range(1, 26)]
        self.correct_button_index = random.randint(0, len(self.buttons) - 1)
        self.buttons[self.correct_button_index] = (str(self.correct_button_index + 1), discord.ButtonStyle.green, "✅")

        for label, style, emoji in self.buttons:
            button = discord.ui.Button(label='', style=style, emoji=emoji, custom_id=label)
            button.callback = self.callback
            self.add_item(button)

    async def on_timeout(self):
        self.disable_all_items()
        if self.message:
            try:
                await self.message.edit(content="**You took too long to respond!**", view=None)
            except discord.NotFound:
                pass

    async def callback(self, interaction: discord.Interaction):
        self.disable_all_items()
        if interaction.custom_id == str(self.correct_button_index + 1):
            await interaction.response.edit_message(content="**✅ Correct option!**", view=None)
            await interaction.user.add_roles(interaction.guild.get_role(dankware_server_role))
        else:
            await interaction.response.edit_message(content="**❌ Wrong option!**", view=None)

@commands.guild_only()
@bot.slash_command(name="youtube", description="Youtube", guild_ids=[dankware_server])
async def youtube_cmd(ctx: discord.ApplicationContext):
    await ctx.respond(socials['youtube'])

@commands.guild_only()
@bot.slash_command(name="telegram", description="Telegram", guild_ids=[dankware_server])
async def telegram_cmd(ctx: discord.ApplicationContext):
    await ctx.respond(socials['telegram'])

@commands.guild_only()
@bot.slash_command(name="verify", description="Verify yourself to get access to the server!", guild_ids=[dankware_server])
async def verify_cmd(ctx: discord.ApplicationContext):
    if ctx.guild.get_role(dankware_server_role) in ctx.author.roles:
        await ctx.respond("**You have already been verified 💀**", delete_after=10)
    else:
        view = VerificationView(timeout=5)
        view.message = await ctx.respond("**Click the green button to get verified!**", view=view, delete_after=10, ephemeral=True)

@commands.guild_only()
@bot.slash_command(name="stats", description="Displays stats", guild_ids=[dankware_server])
async def stats_cmd(ctx: discord.ApplicationContext):
    with Session(engine) as session:
        description = f"""**uptime:** `{get_duration(start_time)}`
**mobile app runs:** `{run_counter['mobile_app']['count']}`
**dank.tool runs:** `{run_counter['dank.tool']['count']}`
**dank.tool users:** `{len(chatroom_sessions)}`
**java servers:** `{session.exec(select(func.count(Java.ip))).one()}`\n**bedrock servers:** `{session.exec(select(func.count(Bedrock.ip))).one()}`
""" # pylint: disable=not-callable
    await ctx.respond(embed=discord.Embed(title="Stats", description=description, color=discord.Color.red()))

@commands.guild_only()
@bot.slash_command(name="check-java-server", description="Shows the status of a minecraft java server", guild_ids=[dankware_server])
async def check_java_server_cmd(ctx: discord.ApplicationContext, ip: str):
    if ip.count('.') != 3 or len(ip) < 7:
        await ctx.respond(f"**Invalid IP:** `{ip}`", delete_after=10)
    _socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    _socket.settimeout(1)
    if _socket.connect_ex((ip,25565)) == 0:
        status = JavaServer(ip,25565).status()
        description = remove_mc_colours(status.description.replace('\n',' '))
        description = f"""**version:** `{status.version.name}`
**players:** `{status.players.online}/{status.players.max}`
**latency:** `{int(status.latency)}ms`
**description:** `{description}`
"""
        await ctx.respond(embed=discord.Embed(title=ip, description=description, color=discord.Color.red()))
        save_server_info("java", ip)
    else:
        await ctx.respond("**Server not found!**", delete_after=10)

@commands.guild_only()
@bot.slash_command(name="check-bedrock-server", description="Shows the status of a minecraft bedrock server", guild_ids=[dankware_server])
async def check_bedrock_server_cmd(ctx: discord.ApplicationContext, ip: str):
    if ip.count('.') != 3 or len(ip) < 7:
        await ctx.respond(f"**Invalid IP:** `{ip}`", delete_after=10)
    else:
        try:
            socket.socket(socket.AF_INET, socket.SOCK_DGRAM).sendto(b'\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xff\x00\xfe\xfe\xfe\xfe\xfd\xfd\xfd\xfd\x124Vx\x00\x00\x00\x00\x00\x00\x00\x00', (ip, 19132))
            status = BedrockServer(ip,19132).status()
            description = remove_mc_colours(str(status.motd.raw).replace('\n',' '))
            description = f"""**version:** `{status.version.name}`
**players:** `{status.players.online}/{status.players.max}`
**latency:** `{int(status.latency)}ms`
**gamemode:** `{status.gamemode}`
**map:** `{status.map_name}`
**motd:** `{description}`
"""
            await ctx.respond(embed=discord.Embed(title=ip, description=description, color=discord.Color.red()))
            save_server_info("bedrock", ip)
        except:
            await ctx.respond("**Server not found!**", delete_after=10)

def start_dank_bot():
    try: bot.run(dank_bot_token)
    except Exception as exc:
        error = err((type(exc), exc, exc.__traceback__),'mini')
        try: requests.post(server_error_webhook, json={"content": f"```<--- ⚠️ Discord Bot --->\n\n{error}```"}, timeout=3)
        except Exception as exc: print(clr(f'  - [ERROR] start_dank_bot: {str(exc)}',2))

print(clr("  - Starting Discord Bot..."))
executor.submit(start_dank_bot)

end_time = datetime.datetime.now()
try: requests.post(server_webhook, json={"content": f"```✅ dank.server online! #{os.getppid()}-{os.getpid()} took {get_duration(start_time, end_time, interval='seconds')}s!```"}, timeout=3)
except Exception as exc: print(clr(f'  - [ERROR] server_webhook: {str(exc)}',2))
time.sleep(5)

if __name__ == "__main__":
    print(clr("  - Starting SocketIO App..."))
    socketio.run(app, host='0.0.0.0', port=int(os.environ.get("PORT", 10000))) # ssl_context='adhoc'
