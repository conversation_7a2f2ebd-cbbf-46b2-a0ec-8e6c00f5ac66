# Exported from Render on 2025-02-21T11:27:01Z
services:
- type: web
  name: dankware
  runtime: python
  repo: https://github.com/SirDank/dank.site-private
  plan: free
  branch: main
  envVars:
  - key: PYTHON_VERSION
    value: 3.12.10
    sync: true
  - key: POETRY_VERSION
    value: 2.1.2
    sync: true
  - key: NODE_VERSION
    value: 23.8.0
    sync: true
  region: oregon
  buildCommand: pip install --upgrade pip; pip install -U setuptools; pip install
    -U wheel; pip install -r requirements.txt
  startCommand: gunicorn --bind 0.0.0.0:10000 -c gunicorn_config.py -w 1 --threads
    100 dank-site:app
  autoDeploy: true

version: "1"
