<!DOCTYPE html>
<html lang="en-US" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="System statistics and monitoring for the server">
    <meta name="keywords" content="system stats, server monitoring, cpu usage, memory usage">
    <meta name="author" content="SirDank">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>System Stats - Dankware</title>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Source Code Pro:wght@400;700&family=Pirata+One&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.css">
    <!-- Common CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common.css') }}">
    <style>
        /* System Stats specific styles */
        .chart-container {
            position: relative;
            margin: 20px auto;
            height: 300px;
            width: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 10px;
            box-shadow: 0 0 15px var(--glow-color);
            padding: 15px;
        }

        .stats-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stats-details {
            font-size: 0.9rem;
            color: #aaa;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <!-- Page Preloader -->
    <div class="preloader">
        <div class="spinner"></div>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">Dankware</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/minecraft-java-servers">Java Servers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/minecraft-bedrock-servers">Bedrock Servers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/system-stats">System Stats</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://github.com/SirDank" target="_blank">GitHub</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-5 pt-5">
        <h1 class="text-center mb-4">System Statistics</h1>

        <!-- Summary Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6">
                <div class="stats-card">
                    <h3>CPU Usage</h3>
                    <div class="stats-value" id="cpu-usage">0%</div>
                    <div class="stats-details">
                        <span id="cpu-freq">0 GHz</span> | <span id="cpu-cores">0 cores</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-danger" id="cpu-progress" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stats-card">
                    <h3>Memory Usage</h3>
                    <div class="stats-value" id="memory-usage">0%</div>
                    <div class="stats-details">
                        <span id="memory-used">0 GB</span> / <span id="memory-total">0 GB</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-danger" id="memory-progress" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stats-card">
                    <h3>Disk Usage</h3>
                    <div class="stats-value" id="disk-usage">0%</div>
                    <div class="stats-details">
                        <span id="disk-used">0 GB</span> / <span id="disk-total">0 GB</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-danger" id="disk-progress" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stats-card">
                    <h3>System Uptime</h3>
                    <div class="stats-value" id="system-uptime">0d 0h 0m</div>
                    <div class="stats-details">
                        <span id="network-traffic">↑ 0 KB/s | ↓ 0 KB/s</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="chart-container">
                    <canvas id="cpuChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <canvas id="memoryChart"></canvas>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="chart-container network-chart-container">
                    <canvas id="networkChart"></canvas>
                </div>
            </div>
        </div>

        <style>
            .network-chart-container {
                height: 350px; /* Taller than standard chart containers */
            }
        </style>

        <!-- Process Table -->
        <div class="row">
            <div class="col-12">
                <div class="stats-card">
                    <h3>Running Processes</h3>
                    <div class="table-responsive">
                        <table class="process-table">
                            <thead>
                                <tr>
                                    <th>PID</th>
                                    <th>Name</th>
                                    <th>CPU %</th>
                                    <th>Memory %</th>
                                    <th>Status</th>
                                    <th>Started</th>
                                    <th>User</th>
                                </tr>
                            </thead>
                            <tbody id="process-table-body">
                                <!-- Process data will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="text-center text-white py-4 mt-5">
        <p>&copy; 2025 Dankware. All rights reserved.</p>
    </footer>

    <!-- Back to top button -->
    <button type="button" class="btn btn-danger btn-floating btn-lg" id="btn-back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up" aria-hidden="true"></i>
    </button>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.js"></script>

    <!-- Custom Scripts -->
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>

    <script>

        // Chart configurations
        const cpuChartCtx = document.getElementById('cpuChart').getContext('2d');
        const memoryChartCtx = document.getElementById('memoryChart').getContext('2d');
        const networkChartCtx = document.getElementById('networkChart').getContext('2d');

        // Common chart options
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            },
            scales: {
                xAxes: [{
                    gridLines: {
                        color: 'rgba(255, 0, 0, 0.1)',
                        zeroLineColor: 'rgba(255, 0, 0, 0.2)'
                    },
                    ticks: {
                        fontColor: '#fff'
                    }
                }],
                yAxes: [{
                    gridLines: {
                        color: 'rgba(255, 0, 0, 0.1)',
                        zeroLineColor: 'rgba(255, 0, 0, 0.2)'
                    },
                    ticks: {
                        fontColor: '#fff',
                        beginAtZero: true
                    }
                }]
            },
            legend: {
                labels: {
                    fontColor: '#fff'
                }
            },
            tooltips: {
                mode: 'index',
                intersect: false
            },
            hover: {
                mode: 'nearest',
                intersect: true
            }
        };

        // Fixed scale options for CPU and Memory charts (0-100%)
        const fixedScaleOptions = {
            ...chartOptions,
            scales: {
                ...chartOptions.scales,
                yAxes: [{
                    ...chartOptions.scales.yAxes[0],
                    ticks: {
                        ...chartOptions.scales.yAxes[0].ticks,
                        min: 0,
                        max: 100,
                        stepSize: 20
                    }
                }]
            }
        };

        // CPU Chart
        const cpuChart = new Chart(cpuChartCtx, {
            type: 'line',
            data: {
                labels: Array(20).fill(''),
                datasets: [{
                    label: 'CPU Usage %',
                    data: Array(20).fill(0),
                    borderColor: 'rgba(255, 0, 0, 1)',
                    backgroundColor: 'rgba(255, 0, 0, 0.1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(255, 0, 0, 1)',
                    pointBorderColor: '#fff',
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    fill: true
                }]
            },
            options: {
                ...fixedScaleOptions,  // Use fixed scale options for 0-100 range
                title: {
                    display: true,
                    text: 'CPU Usage Over Time',
                    fontColor: '#fff',
                    fontSize: 16
                }
            }
        });

        // Memory Chart
        const memoryChart = new Chart(memoryChartCtx, {
            type: 'line',
            data: {
                labels: Array(20).fill(''),
                datasets: [{
                    label: 'Memory Usage %',
                    data: Array(20).fill(0),
                    borderColor: 'rgba(255, 0, 0, 1)',
                    backgroundColor: 'rgba(255, 0, 0, 0.1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(255, 0, 0, 1)',
                    pointBorderColor: '#fff',
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    fill: true
                }]
            },
            options: {
                ...fixedScaleOptions,  // Use fixed scale options for 0-100 range
                title: {
                    display: true,
                    text: 'Memory Usage Over Time',
                    fontColor: '#fff',
                    fontSize: 16
                }
            }
        });

        // Disk Chart removed

        // Network Chart
        const networkChart = new Chart(networkChartCtx, {
            type: 'line',
            data: {
                labels: Array(30).fill(''),  // Increased data points for wider chart
                datasets: [
                    {
                        label: 'Sent (KB/s)',
                        data: Array(30).fill(0),  // Increased data points
                        borderColor: 'rgba(255, 0, 0, 1)',
                        backgroundColor: 'rgba(255, 0, 0, 0.05)',  // Added slight background
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(255, 0, 0, 1)',
                        pointBorderColor: '#fff',
                        pointRadius: 2,  // Smaller points for cleaner look
                        pointHoverRadius: 5,
                        fill: true  // Fill area under the line
                    },
                    {
                        label: 'Received (KB/s)',
                        data: Array(30).fill(0),  // Increased data points
                        borderColor: 'rgba(255, 165, 0, 1)',
                        backgroundColor: 'rgba(255, 165, 0, 0.05)',  // Added slight background
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(255, 165, 0, 1)',
                        pointBorderColor: '#fff',
                        pointRadius: 2,  // Smaller points for cleaner look
                        pointHoverRadius: 5,
                        fill: true  // Fill area under the line
                    }
                ]
            },
            options: {
                ...chartOptions,
                title: {
                    display: true,
                    text: 'Network Traffic',
                    fontColor: '#fff',
                    fontSize: 18  // Slightly larger title
                }
            }
        });

        // Connect to SSE for real-time updates
        const eventSource = new EventSource('/system-stats-data');

        // Handle connection errors
        eventSource.onerror = function(event) {
            console.error('SSE connection error');
            // Try to reconnect after 5 seconds
            setTimeout(() => {
                console.log('Attempting to reconnect...');
                // Instead of reloading the page, we'll try to create a new connection
                if (eventSource.readyState === EventSource.CLOSED) {
                    window.location.reload();
                }
            }, 5000);
        };

        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);

                // Check if there's an error message from the server
                if (data.error) {
                    console.error('Server error:', data.error);
                    return;
                }

                // Update summary stats
                // CPU
                document.getElementById('cpu-usage').textContent = data.cpu.usage.toFixed(1) + '%';
                document.getElementById('cpu-progress').style.width = data.cpu.usage + '%';
                document.getElementById('cpu-progress').setAttribute('aria-valuenow', data.cpu.usage);
                document.getElementById('cpu-freq').textContent = data.cpu.freq_current.toFixed(2) + ' GHz';
                document.getElementById('cpu-cores').textContent = data.cpu.cores_logical + ' cores (' + data.cpu.cores_physical + ' physical)';

                // Memory
                document.getElementById('memory-usage').textContent = data.memory.percent.toFixed(1) + '%';
                document.getElementById('memory-progress').style.width = data.memory.percent + '%';
                document.getElementById('memory-progress').setAttribute('aria-valuenow', data.memory.percent);
                // Format memory values with commas
                document.getElementById('memory-used').textContent = data.memory.used_mb.toLocaleString() + ' MB';
                document.getElementById('memory-total').textContent = data.memory.total_mb.toLocaleString() + ' MB';

                // Disk
                document.getElementById('disk-usage').textContent = data.disk.percent.toFixed(1) + '%';
                document.getElementById('disk-progress').style.width = data.disk.percent + '%';
                document.getElementById('disk-progress').setAttribute('aria-valuenow', data.disk.percent);
                document.getElementById('disk-used').textContent = data.disk.used_gb.toFixed(2) + ' GB';
                document.getElementById('disk-total').textContent = data.disk.total_gb.toFixed(2) + ' GB';

                // System & Network
                document.getElementById('system-uptime').textContent = data.system.uptime;
                document.getElementById('network-traffic').textContent =
                    '↑ ' + data.network.sent_kb.toFixed(2) + ' KB/s | ↓ ' + data.network.recv_kb.toFixed(2) + ' KB/s';

                // Update charts
                updateChart(cpuChart, data.time, data.cpu.usage);
                updateChart(memoryChart, data.time, data.memory.percent);

                // Update network chart
                updateNetworkChart(networkChart, data.time, data.network.sent_kb, data.network.recv_kb);

                // Update process table
                updateProcessTable(data.processes);
            } catch (error) {
                console.error('Error processing SSE data:', error);
            }
        };

        // Error handler is already defined above

        // Helper function to update charts
        function updateChart(chart, label, value) {
            chart.data.labels.shift();
            chart.data.labels.push(label);

            chart.data.datasets[0].data.shift();
            chart.data.datasets[0].data.push(value);

            chart.update();
        }

        // Helper function to update network chart
        function updateNetworkChart(chart, label, sentValue, recvValue) {
            // Remove oldest data point
            chart.data.labels.shift();
            chart.data.labels.push(label);

            chart.data.datasets[0].data.shift();
            chart.data.datasets[0].data.push(sentValue);

            chart.data.datasets[1].data.shift();
            chart.data.datasets[1].data.push(recvValue);

            // Use a more efficient update with less animation for smoother performance
            chart.update({
                duration: 300,
                easing: 'linear'
            });
        }

        // Helper function to update process table
        function updateProcessTable(processes) {
            const tableBody = document.getElementById('process-table-body');
            tableBody.innerHTML = '';

            processes.forEach(process => {
                const row = document.createElement('tr');

                // Add classes for styling based on CPU usage
                let cpuClass = '';
                if (process.cpu_percent > 25) {
                    cpuClass = 'text-danger fw-bold';
                } else if (process.cpu_percent > 10) {
                    cpuClass = 'text-warning';
                }

                // Add classes for styling based on memory usage
                let memClass = '';
                if (process.memory_percent > 10) {
                    memClass = 'text-danger fw-bold';
                } else if (process.memory_percent > 5) {
                    memClass = 'text-warning';
                }

                row.innerHTML = `
                    <td>${process.pid}</td>
                    <td><span title="${process.name}">${process.name.length > 20 ? process.name.substring(0, 20) + '...' : process.name}</span></td>
                    <td class="${cpuClass}">${process.cpu_percent.toFixed(1)}%</td>
                    <td class="${memClass}">${process.memory_percent.toFixed(1)}%</td>
                    <td>${process.status}</td>
                    <td>${process.created}</td>
                    <td>${process.username}</td>
                `;

                tableBody.appendChild(row);
            });
        }
    </script>
</body>
</html>
