import logging
from gunicorn import glogging


class CustomGunicornLogger(glogging.Logger):

    def setup(self, cfg):
        super().setup(cfg)

        # Add filters to Gunicorn logger
        logger = logging.getLogger("gunicorn.access")
        logger.addFilter(_Filter())

class _Filter(logging.Filter):
    def filter(self, record):
        return False

accesslog = '-'
logger_class = CustomGunicornLogger
