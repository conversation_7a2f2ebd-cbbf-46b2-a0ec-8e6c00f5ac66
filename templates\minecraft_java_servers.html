<!DOCTYPE html>
<html lang="en-US" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Explore Minecraft Java servers found around the world by the users of the server scanner in dank.tool">
    <meta name="keywords" content="minecraft java servers, minecraft servers, minecraft server list">
    <meta name="author" content="SirDank">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Minecraft Java Servers - Dankware</title>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Source Code Pro:wght@400;700&family=Pirata+One&display=swap" rel="stylesheet">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common.css') }}">
</head>
<body>
    <!-- Page Preloader -->
    <div class="preloader" aria-hidden="true">
        <div class="loader" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">DANKWARE</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/minecraft-java-servers">Java Servers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/minecraft-bedrock-servers">Bedrock Servers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/system-stats">System Stats</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://github.com/SirDank" target="_blank">GitHub</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-5 pt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4 mt-4">Minecraft Java Servers</h1>

                <!-- Server Count and Page Info -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="text-muted">
                        Total Servers: {{ pagination.total_count }}
                    </div>
                    <div class="text-muted">
                        Page {{ pagination.page }} of {{ pagination.total_pages }}
                    </div>
                </div>

                <!-- Search Form -->
                <div class="mb-4">
                    <form id="searchForm" method="GET" action="/minecraft-java-servers" class="d-flex">
                        <input type="hidden" name="page" value="1">
                        <input type="hidden" name="per_page" value="{{ pagination.per_page }}">
                        <input type="hidden" name="sort_by" value="{{ pagination.sort_by }}">
                        <input type="hidden" name="sort_order" value="{{ pagination.sort_order }}">
                        <div class="input-group">
                            <input type="text" name="search" id="serverSearch" class="form-control"
                                placeholder="Search servers..." value="{{ search_query }}"
                                aria-label="Search servers">
                            <button class="btn btn-danger" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                            {% if search_query %}
                            <a href="/minecraft-java-servers" class="btn btn-outline-danger">
                                <i class="fas fa-times"></i> Clear
                            </a>
                            {% endif %}
                        </div>
                    </form>
                </div>

                <!-- Controls Row -->
                <div class="row mb-3">
                    <!-- Sort Controls -->
                    <div class="col-md-8">
                        <div class="d-flex flex-wrap gap-3 align-items-center">
                            <div class="form-group d-flex align-items-center">
                                <label for="sortBySelect" class="me-2 text-muted">Sort by:</label>
                                <select id="sortBySelect" class="form-select form-select-sm" style="width: 145px;">
                                    <option value="ip" {% if pagination.sort_by == 'ip' %}selected{% endif %}>IP Address</option>
                                    <option value="version" {% if pagination.sort_by == 'version' %}selected{% endif %}>Version</option>
                                    <option value="players" {% if pagination.sort_by == 'players' %}selected{% endif %}>Players</option>
                                    <option value="latency" {% if pagination.sort_by == 'latency' %}selected{% endif %}>Latency</option>
                                    <option value="description" {% if pagination.sort_by == 'description' %}selected{% endif %}>Description</option>
                                    <option value="city" {% if pagination.sort_by == 'city' %}selected{% endif %}>City</option>
                                    <option value="org" {% if pagination.sort_by == 'org' %}selected{% endif %}>Organization</option>
                                    <option value="domain" {% if pagination.sort_by == 'domain' %}selected{% endif %}>Domain</option>
                                    <option value="last_checked" {% if pagination.sort_by == 'last_checked' %}selected{% endif %}>Last Checked</option>
                                </select>
                            </div>
                            <div class="form-group d-flex align-items-center">
                                <label for="sortOrderSelect" class="me-2 text-muted">Order:</label>
                                <select id="sortOrderSelect" class="form-select form-select-sm" style="width: 130px;">
                                    <option value="asc" {% if pagination.sort_order == 'asc' %}selected{% endif %}>Ascending</option>
                                    <option value="desc" {% if pagination.sort_order == 'desc' %}selected{% endif %}>Descending</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- Per Page Selector -->
                    <div class="col-md-4">
                        <div class="d-flex justify-content-end">
                            <div class="form-group d-flex align-items-center">
                                <label for="perPageSelect" class="me-2 text-muted">Servers per page:</label>
                                <select id="perPageSelect" class="form-select form-select-sm" style="width: 65px;">
                                    <option value="10" {% if pagination.per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="25" {% if pagination.per_page == 25 %}selected{% endif %}>25</option>
                                    <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50</option>
                                    <option value="100" {% if pagination.per_page == 100 %}selected{% endif %}>100</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="javaTable" class="display table table-dark table-hover nowrap w-100">
                        <thead>
                            <tr>
                                <th>IP</th>
                                <th>Version</th>
                                <th>Players</th>
                                <th>Latency</th>
                                <th>Description</th>
                                <th>City</th>
                                <th>Org</th>
                                <th>Domain</th>
                                <th>Last Checked</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ip, server in java_servers.items() %}
                                <tr>
                                    <td data-order="{{ ip.split('.')[0] }}">{{ ip }}</td>
                                    <td>{{ server.version }}</td>
                                    <td data-order="{{ server.players.split('/')[0] }}">{{ server.players }}</td>
                                    <td>{{ server.latency }}ms</td>
                                    <td>{{ server.description }}</td>
                                    <td>{{ server.city }}</td>
                                    <td>{{ server.org }}</td>
                                    <td>{{ server.domain }}</td>
                                    <td>{{ server.last_checked }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Controls -->
                <nav aria-label="Server pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                            <a class="page-link" href="?page=1&per_page={{ pagination.per_page }}&sort_by={{ pagination.sort_by }}&sort_order={{ pagination.sort_order }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                            <a class="page-link" href="?page={{ pagination.page - 1 }}&per_page={{ pagination.per_page }}&sort_by={{ pagination.sort_by }}&sort_order={{ pagination.sort_order }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>

                        {% set start_page = [pagination.page - 2, 1] | max %}
                        {% set end_page = [start_page + 4, pagination.total_pages] | min %}
                        {% set start_page = [end_page - 4, 1] | max %}

                        {% for p in range(start_page, end_page + 1) %}
                            <li class="page-item {% if p == pagination.page %}active{% endif %}">
                                <a class="page-link" href="?page={{ p }}&per_page={{ pagination.per_page }}&sort_by={{ pagination.sort_by }}&sort_order={{ pagination.sort_order }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ p }}</a>
                            </li>
                        {% endfor %}

                        <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                            <a class="page-link" href="?page={{ pagination.page + 1 }}&per_page={{ pagination.per_page }}&sort_by={{ pagination.sort_by }}&sort_order={{ pagination.sort_order }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                            <a class="page-link" href="?page={{ pagination.total_pages }}&per_page={{ pagination.per_page }}&sort_by={{ pagination.sort_by }}&sort_order={{ pagination.sort_order }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </main>

    <!-- Back to top button -->
    <button type="button" class="btn btn-danger btn-floating btn-lg" id="btn-back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up" aria-hidden="true"></i>
    </button>

    <!-- External Libraries -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js" integrity="sha512-QoJS4DOhdmG8kbbHkxmB/rtPdN62cGWXAdAFWWJPvUFF1/zxcPSdAnn4HhYZSIlVoLVEJ0LesfNlusgm2bPfnA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!-- Custom Scripts -->
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>
    <script src="{{ url_for('static', filename='js/minecraft_servers.js') }}"></script>
</body>
</html>
